#!/usr/bin/env python3
"""
Simple test for folder validation functionality without Qt dependencies.
"""

import sys
import os
import re

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def validate_folder_name(folder_name):
    """
    Validate folder name for filesystem compatibility
    (Copied from ui_components.py for testing)
    
    Args:
        folder_name (str): Proposed folder name
        
    Returns:
        dict: {'valid': bool, 'error': str}
    """
    if not folder_name or not folder_name.strip():
        return {'valid': False, 'error': 'Folder name cannot be empty'}

    # Store original for space checking
    original_name = folder_name
    folder_name = folder_name.strip()
    
    # Check length
    if len(folder_name) > 100:
        return {'valid': False, 'error': 'Folder name too long (max 100 characters)'}
    
    # Check for invalid characters (Windows/Linux compatible)
    invalid_chars = r'[<>:"/\\|?*]'
    if re.search(invalid_chars, folder_name):
        return {'valid': False, 'error': 'Contains invalid characters: < > : " / \\ | ? *'}
    
    # Check for reserved names (Windows)
    reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                     'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                     'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
    if folder_name.upper() in reserved_names:
        return {'valid': False, 'error': f'"{folder_name}" is a reserved system name'}
    
    # Check if it starts or ends with space or period (before stripping)
    if original_name.startswith(' ') or original_name.endswith(' '):
        return {'valid': False, 'error': 'Folder name cannot start or end with spaces'}

    if folder_name.startswith('.') or folder_name.endswith('.'):
        return {'valid': False, 'error': 'Folder name cannot start or end with periods'}
    
    return {'valid': True, 'error': None}

def test_folder_validation():
    """Test folder name validation functionality"""
    print("=" * 60)
    print("Testing Folder Name Validation")
    print("=" * 60)
    
    # Test valid folder names
    valid_names = [
        "test_scan_001",
        "My Scan Folder",
        "scan-2024-06-30",
        "experiment_batch_1",
        "Test123",
        "a" * 50  # 50 characters
    ]
    
    print("Testing valid folder names:")
    for name in valid_names:
        result = validate_folder_name(name)
        if result['valid']:
            print(f"  ✓ '{name}' - Valid")
        else:
            print(f"  ✗ '{name}' - Invalid: {result['error']}")
            return False
    
    # Test invalid folder names
    invalid_names = [
        "",  # Empty
        "   ",  # Only spaces
        "test<file>",  # Invalid characters
        "test/file",  # Invalid characters
        "test\\file",  # Invalid characters
        "test|file",  # Invalid characters
        "CON",  # Reserved name
        "PRN",  # Reserved name
        " test",  # Starts with space
        "test ",  # Ends with space
        ".test",  # Starts with period
        "test.",  # Ends with period
        "a" * 101  # Too long
    ]
    
    print("\nTesting invalid folder names:")
    for name in invalid_names:
        result = validate_folder_name(name)
        if not result['valid']:
            print(f"  ✓ '{name}' - Correctly rejected: {result['error']}")
        else:
            print(f"  ✗ '{name}' - Should be invalid but was accepted")
            return False
    
    print("\n✓ Folder validation test completed successfully!")
    return True

def test_folder_structure():
    """Test folder structure creation logic"""
    print("\n" + "=" * 60)
    print("Testing Folder Structure Logic")
    print("=" * 60)
    
    import tempfile
    
    try:
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Test folder creation
                test_folder_name = "test_scan_folder"
                scan_folder_path = os.path.join(os.getcwd(), test_folder_name)
                
                # Create main folder
                os.makedirs(scan_folder_path)
                print(f"✓ Main folder created: {scan_folder_path}")
                
                # Create debug_screenshots subfolder
                debug_folder_path = os.path.join(scan_folder_path, 'debug_screenshots')
                os.makedirs(debug_folder_path)
                print(f"✓ Debug subfolder created: {debug_folder_path}")
                
                # Verify folder exists
                if os.path.exists(scan_folder_path):
                    print("✓ Main folder exists")
                else:
                    print("✗ Main folder does not exist")
                    return False
                
                # Verify debug subfolder exists
                if os.path.exists(debug_folder_path):
                    print("✓ Debug subfolder exists")
                else:
                    print("✗ Debug subfolder does not exist")
                    return False
                
                # Test write permissions
                test_file = os.path.join(scan_folder_path, 'test_write.txt')
                with open(test_file, 'w') as f:
                    f.write("test")
                
                if os.path.exists(test_file):
                    print("✓ Write permissions verified")
                    os.remove(test_file)
                else:
                    print("✗ Write permission test failed")
                    return False
                
            finally:
                os.chdir(original_cwd)
        
        print("\n✓ Folder structure test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Folder structure test failed: {str(e)}")
        return False

def test_file_paths():
    """Test file path generation logic"""
    print("\n" + "=" * 60)
    print("Testing File Path Generation")
    print("=" * 60)
    
    import tempfile
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create test folder structure
                test_folder = "test_scan_paths"
                os.makedirs(test_folder)
                os.makedirs(os.path.join(test_folder, 'debug_screenshots'))
                
                # Test CSV file path
                timestamp = 1672531200
                csv_filename = f'scan_adaptive_{timestamp}.csv'
                csv_path = os.path.join(test_folder, csv_filename)
                print(f"✓ CSV path: {csv_path}")
                
                # Test reference file path
                base_name = f'scan_adaptive_{timestamp}'
                reference_filename = f"{base_name}_chip_reference.json"
                reference_path = os.path.join(test_folder, reference_filename)
                print(f"✓ Reference path: {reference_path}")
                
                # Test log file path
                log_filename = f"{base_name}_log.txt"
                log_path = os.path.join(test_folder, log_filename)
                print(f"✓ Log path: {log_path}")
                
                # Test debug screenshot path
                debug_screenshot = os.path.join(test_folder, 'debug_screenshots', f'corner_reference_{timestamp}.png')
                print(f"✓ Debug screenshot path: {debug_screenshot}")
                
                # Verify all paths are within the test folder
                expected_folder = os.path.join(temp_dir, test_folder)
                for path in [csv_path, reference_path, log_path, debug_screenshot]:
                    # Convert to absolute path for comparison
                    abs_path = os.path.abspath(path)
                    abs_expected = os.path.abspath(expected_folder)

                    if abs_path.startswith(abs_expected):
                        print(f"  ✓ Path is correctly within scan folder: {os.path.basename(path)}")
                    else:
                        print(f"  ✗ Path is outside scan folder: {path}")
                        print(f"    Expected to start with: {abs_expected}")
                        print(f"    Actual path: {abs_path}")
                        return False
                
            finally:
                os.chdir(original_cwd)
        
        print("\n✓ File path generation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ File path generation test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Scan Folder Organization System (Simple)")
    print("=" * 80)
    
    tests = [
        ("Folder Name Validation", test_folder_validation),
        ("Folder Structure Logic", test_folder_structure),
        ("File Path Generation", test_file_paths),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} Test PASSED")
        else:
            print(f"❌ {test_name} Test FAILED")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scan folder organization logic is working correctly.")
        print("\nKey Features Verified:")
        print("✓ Comprehensive folder name validation")
        print("✓ Proper folder structure creation")
        print("✓ Correct file path generation within custom folders")
        print("✓ Write permission verification")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
