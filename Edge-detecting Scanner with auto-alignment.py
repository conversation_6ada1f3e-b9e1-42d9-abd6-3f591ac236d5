"""
Complete Integrated Scanning System with Edge Detection and Auto-Alignment
Includes all features:
1. Grid and adaptive scanning modes
2. Edge detection (Background, General, None)
3. Auto-alignment for repositioned chips
4. Shape matching and coordinate transformation

Key coordinate system:
- Stage: Y-axis horizontal, X-axis vertical
- move_absolute(y_um, x_um) - Y first, X second
"""

import os
import sys
import csv
import time
import ast
import mss
import numpy as np
import cv2
import math
import json
from dataclasses import dataclass
from typing import List, Tu<PERSON>, Dict, Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QPushButton, QProgressBar, QLabel,
    QVBoxLayout, QWidget, QFileDialog, QInputDialog, QHBoxLayout,
    QSplitter, QListWidget, QGraphicsView, QGraphicsScene,
    QAbstractItemView, QGraphicsEllipseItem, QSizePolicy, QGraphicsPolygonItem,
    QMessageBox, QComboBox, QCheckBox, QToolBar
)
from PyQt6.QtGui import QPolygonF, QPen, QBrush, QColor, QTransform, QPixmap, QImage
from PyQt6.QtCore import Qt, QPointF, QSize, QThread, pyqtSignal

from inference_sdk import InferenceHTTPClient
from MCM301_COMMAND_LIB import MCM301
import supervision as sv

# --- Constants ---
# STEP_Y_UM = 691.18  # Horizontal step size
# STEP_X_UM = 490.37  # Vertical step size

STEP_Y_UM = 139.56
STEP_X_UM = 99.24

# --- Detection & Annotation Setup ---
detection_client = InferenceHTTPClient(
    api_url="https://detect.roboflow.com",
    api_key="8KXHakThGju0bGhPCTCH"
)
selected_classes = {0, 2, 3}
polygon_annotator = sv.PolygonAnnotator()
label_annotator = sv.LabelAnnotator()

# --- Auto-Alignment Classes ---
@dataclass
class Flake:
    """Data structure for a flake with shape analysis capabilities"""
    id: str
    center_x: float
    center_y: float
    real_x_um: float
    real_y_um: float
    shape: List[Tuple[float, float]]
    class_name: str
    area: float = 0.0
    perimeter: float = 0.0
    hu_moments: np.ndarray = None
    shape_descriptor: np.ndarray = None
    
    def __post_init__(self):
        """Calculate shape properties after initialization"""
        if self.shape:
            self.calculate_shape_properties()
    
    def calculate_shape_properties(self):
        """Calculate area, perimeter, and shape descriptors"""
        pts = np.array(self.shape, dtype=np.float32)
        
        self.area = cv2.contourArea(pts)
        self.perimeter = cv2.arcLength(pts, True)
        
        moments = cv2.moments(pts)
        self.hu_moments = cv2.HuMoments(moments).flatten()
        
        x, y, w, h = cv2.boundingRect(pts)
        aspect_ratio = w / h if h > 0 else 1.0
        circularity = 4 * np.pi * self.area / (self.perimeter ** 2) if self.perimeter > 0 else 0
        
        self.shape_descriptor = np.array([
            self.area / 1000.0,
            self.perimeter / 100.0,
            aspect_ratio,
            circularity
        ])


class ShapeMatcher:
    """Matches flakes based on shape similarity"""
    
    def __init__(self, hu_weight=0.5, descriptor_weight=0.3, class_weight=0.2):
        self.hu_weight = hu_weight
        self.descriptor_weight = descriptor_weight
        self.class_weight = class_weight
    
    def calculate_similarity(self, flake1: Flake, flake2: Flake) -> float:
        """Calculate similarity score between two flakes (0-1, higher is more similar)"""
        class_score = 1.0 if flake1.class_name == flake2.class_name else 0.0
        
        hu1 = -np.sign(flake1.hu_moments) * np.log10(np.abs(flake1.hu_moments) + 1e-10)
        hu2 = -np.sign(flake2.hu_moments) * np.log10(np.abs(flake2.hu_moments) + 1e-10)
        hu_distance = np.linalg.norm(hu1 - hu2)
        hu_score = np.exp(-hu_distance / 10.0)
        
        desc_distance = np.linalg.norm(flake1.shape_descriptor - flake2.shape_descriptor)
        desc_score = np.exp(-desc_distance / 5.0)
        
        total_score = (self.hu_weight * hu_score + 
                      self.descriptor_weight * desc_score + 
                      self.class_weight * class_score)
        
        return total_score
    
    def find_matches(self, reference_flakes: List[Flake], 
                    current_flakes: List[Flake], 
                    threshold: float = 0.7) -> List[Tuple[Flake, Flake, float]]:
        """Find matching flakes between reference and current sets"""
        matches = []
        
        for ref_flake in reference_flakes:
            best_match = None
            best_score = 0
            
            for curr_flake in current_flakes:
                score = self.calculate_similarity(ref_flake, curr_flake)
                if score > best_score and score > threshold:
                    best_score = score
                    best_match = curr_flake
            
            if best_match:
                matches.append((ref_flake, best_match, best_score))
        
        matches.sort(key=lambda x: x[2], reverse=True)
        return matches


class CoordinateTransformer:
    """Handles coordinate transformation between reference and current positions"""
    
    def __init__(self):
        self.translation = np.array([0.0, 0.0])
        self.rotation = 0.0
        self.scale = 1.0
        self.transform_matrix = None
    
    def calculate_transform_from_pairs(self, ref_points: np.ndarray, 
                                     curr_points: np.ndarray,
                                     allow_scale: bool = False) -> dict:
        """Calculate transformation from matched point pairs"""
        if len(ref_points) < 2:
            raise ValueError("Need at least 2 point pairs for transformation")
        
        ref_center = np.mean(ref_points, axis=0)
        curr_center = np.mean(curr_points, axis=0)
        
        ref_centered = ref_points - ref_center
        curr_centered = curr_points - curr_center
        
        if len(ref_points) == 2:
            ref_vec = ref_centered[1] - ref_centered[0]
            curr_vec = curr_centered[1] - curr_centered[0]
            
            ref_angle = np.arctan2(ref_vec[1], ref_vec[0])
            curr_angle = np.arctan2(curr_vec[1], curr_vec[0])
            self.rotation = curr_angle - ref_angle
            
            if allow_scale:
                ref_dist = np.linalg.norm(ref_vec)
                curr_dist = np.linalg.norm(curr_vec)
                self.scale = curr_dist / ref_dist if ref_dist > 0 else 1.0
            else:
                self.scale = 1.0
        else:
            # Use SVD for optimal transformation (Kabsch algorithm)
            H = ref_centered.T @ curr_centered
            U, S, Vt = np.linalg.svd(H)
            R = Vt.T @ U.T
            
            if np.linalg.det(R) < 0:
                Vt[-1, :] *= -1
                R = Vt.T @ U.T
            
            self.rotation = np.arctan2(R[1, 0], R[0, 0])
            
            if allow_scale:
                scale_factors = []
                for i in range(len(ref_points)):
                    ref_dist = np.linalg.norm(ref_centered[i])
                    if ref_dist > 0:
                        curr_rotated = R @ ref_centered[i]
                        curr_dist = np.linalg.norm(curr_centered[i])
                        scale_factors.append(curr_dist / ref_dist)
                self.scale = np.median(scale_factors) if scale_factors else 1.0
            else:
                self.scale = 1.0
        
        R_matrix = np.array([[np.cos(self.rotation), -np.sin(self.rotation)],
                           [np.sin(self.rotation), np.cos(self.rotation)]])
        self.translation = curr_center - self.scale * R_matrix @ ref_center
        
        self.transform_matrix = np.eye(3)
        self.transform_matrix[:2, :2] = self.scale * R_matrix
        self.transform_matrix[:2, 2] = self.translation
        
        return {
            'translation': self.translation,
            'rotation': self.rotation,
            'rotation_degrees': np.degrees(self.rotation),
            'scale': self.scale,
            'ref_center': ref_center,
            'curr_center': curr_center
        }
    
    def transform_point(self, point: np.ndarray) -> np.ndarray:
        """Transform a point from reference to current coordinates"""
        if self.transform_matrix is None:
            raise ValueError("Transformation not calculated yet")
        
        point_h = np.append(point, 1)
        transformed_h = self.transform_matrix @ point_h
        return transformed_h[:2]
    
    def inverse_transform_point(self, point: np.ndarray) -> np.ndarray:
        """Transform a point from current to reference coordinates"""
        if self.transform_matrix is None:
            raise ValueError("Transformation not calculated yet")
        
        inv_matrix = np.linalg.inv(self.transform_matrix)
        point_h = np.append(point, 1)
        transformed_h = inv_matrix @ point_h
        return transformed_h[:2]


class ChipAligner:
    """Main class for chip alignment"""
    
    def __init__(self, min_matches: int = 3, ransac_threshold: float = 10.0):
        self.shape_matcher = ShapeMatcher()
        self.transformer = CoordinateTransformer()
        self.min_matches = min_matches
        self.ransac_threshold = ransac_threshold
    
    def select_locator_flakes(self, flakes: List[Flake], 
                            n_locators: int = 5) -> List[Flake]:
        """Select the best locator flakes based on uniqueness and distribution"""
        if len(flakes) <= n_locators:
            return flakes
        
        scores = []
        for i, flake in enumerate(flakes):
            size_score = flake.area / 1000.0
            
            uniqueness = 0
            for j, other in enumerate(flakes):
                if i != j:
                    similarity = self.shape_matcher.calculate_similarity(flake, other)
                    uniqueness += (1 - similarity)
            uniqueness /= (len(flakes) - 1)
            
            score = size_score * 0.4 + uniqueness * 0.6
            scores.append((score, flake))
        
        scores.sort(key=lambda x: x[0], reverse=True)
        
        selected = [scores[0][1]]
        
        for score, flake in scores[1:]:
            min_dist = float('inf')
            for sel_flake in selected:
                dist = np.sqrt((flake.real_x_um - sel_flake.real_x_um)**2 + 
                             (flake.real_y_um - sel_flake.real_y_um)**2)
                min_dist = min(min_dist, dist)
            
            if min_dist > 50:  # 50 micrometers minimum distance
                selected.append(flake)
                if len(selected) >= n_locators:
                    break
        
        return selected
    
    def align_chips(self, reference_flakes: List[Flake], 
                   current_flakes: List[Flake],
                   locator_flakes: Optional[List[Flake]] = None) -> dict:
        """Align current chip to reference position"""
        ref_set = locator_flakes if locator_flakes else reference_flakes
        
        matches = self.shape_matcher.find_matches(ref_set, current_flakes)
        
        if len(matches) < self.min_matches:
            return {
                'success': False,
                'error': f'Insufficient matches: {len(matches)} < {self.min_matches}',
                'matches': matches
            }
        
        ref_points = np.array([[m[0].real_x_um, m[0].real_y_um] for m in matches])
        curr_points = np.array([[m[1].real_x_um, m[1].real_y_um] for m in matches])
        
        # Use RANSAC for robust transformation estimation
        if len(matches) > 3:
            best_inliers = []
            best_transform = None
            
            for _ in range(100):
                indices = np.random.choice(len(matches), 3, replace=False)
                sample_ref = ref_points[indices]
                sample_curr = curr_points[indices]
                
                transformer = CoordinateTransformer()
                try:
                    transform = transformer.calculate_transform_from_pairs(
                        sample_ref, sample_curr, allow_scale=False)
                except:
                    continue
                
                inliers = []
                for i, (ref_pt, curr_pt) in enumerate(zip(ref_points, curr_points)):
                    pred_pt = transformer.transform_point(ref_pt)
                    error = np.linalg.norm(pred_pt - curr_pt)
                    if error < self.ransac_threshold:
                        inliers.append(i)
                
                if len(inliers) > len(best_inliers):
                    best_inliers = inliers
                    best_transform = transform
                    self.transformer = transformer
            
            if best_inliers:
                inlier_ref = ref_points[best_inliers]
                inlier_curr = curr_points[best_inliers]
                transform = self.transformer.calculate_transform_from_pairs(
                    inlier_ref, inlier_curr, allow_scale=False)
        else:
            transform = self.transformer.calculate_transform_from_pairs(
                ref_points, curr_points, allow_scale=False)
            best_inliers = list(range(len(matches)))
        
        errors = []
        aligned_matches = []
        for i, (ref_flake, curr_flake, score) in enumerate(matches):
            ref_pt = np.array([ref_flake.real_x_um, ref_flake.real_y_um])
            curr_pt = np.array([curr_flake.real_x_um, curr_flake.real_y_um])
            pred_pt = self.transformer.transform_point(ref_pt)
            error = np.linalg.norm(pred_pt - curr_pt)
            errors.append(error)
            
            aligned_matches.append({
                'reference_id': ref_flake.id,
                'current_id': curr_flake.id,
                'similarity_score': score,
                'alignment_error': error,
                'is_inlier': i in best_inliers
            })
        
        return {
            'success': True,
            'transform': transform,
            'matches': aligned_matches,
            'num_inliers': len(best_inliers),
            'mean_error': np.mean([e for i, e in enumerate(errors) if i in best_inliers]),
            'max_error': np.max([e for i, e in enumerate(errors) if i in best_inliers]),
            'transformer': self.transformer
        }


def save_chip_reference(chip_id: str, flakes: List[Flake], 
                       locator_flakes: List[Flake], 
                       filepath: str):
    """Save chip reference data including locator flakes"""
    data = {
        'chip_id': chip_id,
        'timestamp': time.time(),
        'all_flakes': [
            {
                'id': f.id,
                'center_x': f.center_x,
                'center_y': f.center_y,
                'real_x_um': f.real_x_um,
                'real_y_um': f.real_y_um,
                'shape': f.shape,
                'class': f.class_name,
                'area': f.area,
                'perimeter': f.perimeter,
                'hu_moments': f.hu_moments.tolist() if f.hu_moments is not None else None
            }
            for f in flakes
        ],
        'locator_flakes': [f.id for f in locator_flakes]
    }
    
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)


def load_chip_reference(filepath: str) -> Tuple[List[Flake], List[str]]:
    """Load chip reference data"""
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    flakes = []
    for f_data in data['all_flakes']:
        flake = Flake(
            id=f_data['id'],
            center_x=f_data['center_x'],
            center_y=f_data['center_y'],
            real_x_um=f_data['real_x_um'],
            real_y_um=f_data['real_y_um'],
            shape=f_data['shape'],
            class_name=f_data['class']
        )
        flakes.append(flake)
    
    return flakes, data['locator_flakes']


# --- Stage Controller ---
class StageController:
    def __init__(self):
        self.dev = MCM301()
        devs = MCM301.list_devices()
        if not devs:
            raise RuntimeError("No stage found")
        sn = devs[0][0]
        if self.dev.open(sn, 115200, 3) < 0 or not self.dev.is_open(sn):
            raise RuntimeError("Cannot open stage")
    
    def move_absolute(self, y_um, x_um):
        """Move to absolute position - Y is horizontal, X is vertical"""
        ctry=[0]; self.dev.convert_nm_to_encoder(5, y_um*1000, ctry)
        ctrx=[0]; self.dev.convert_nm_to_encoder(4, x_um*1000, ctrx)
        self.dev.move_absolute(5, ctry[0])
        self.dev.move_absolute(4, ctrx[0])
    
    def close(self):
        self.dev.close()


# --- Edge Detection Classes ---
class EdgeDetector:
    """Detects edges of silicon chips based on color/intensity differences"""
    
    def __init__(self, threshold_low=50, threshold_high=150, debug=False):
        self.threshold_low = threshold_low
        self.threshold_high = threshold_high
        self.debug = debug
        
    def detect_chip_edges(self, img):
        """
        Detect silicon chip edges in the image
        Returns: mask where True = chip area, False = outside chip
        """
        # Convert to grayscale
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Method 1: Focus on the chip-background boundary using edge detection
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Use Canny edge detection to find all edges
        edges = cv2.Canny(blurred, 30, 100)
        
        # Method 2: Use color to identify background (beige/yellow)
        if len(img.shape) == 3:
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            # Beige background has specific hue and saturation
            # Typical beige: hue ~20-40, moderate saturation
            lower_background = np.array([15, 20, 100])
            upper_background = np.array([45, 255, 255])
            background_mask = cv2.inRange(hsv, lower_background, upper_background)
            
            # The chip is everything that's NOT background
            chip_mask_color = cv2.bitwise_not(background_mask)
        else:
            # If grayscale, use intensity threshold
            # Background is typically lighter than chip
            _, chip_mask_color = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Method 3: Combine edge and color information
        # Dilate edges to create regions
        kernel = np.ones((10, 10), np.uint8)
        edges_dilated = cv2.dilate(edges, kernel, iterations=2)
        
        # Fill holes in the chip mask
        chip_mask_filled = chip_mask_color.copy()
        h, w = chip_mask_filled.shape
        flood_mask = np.zeros((h + 2, w + 2), np.uint8)
        cv2.floodFill(chip_mask_filled, flood_mask, (0, 0), 255)
        chip_mask_filled = cv2.bitwise_not(chip_mask_filled)
        
        # Clean up with morphological operations
        kernel_large = np.ones((20, 20), np.uint8)
        chip_mask_cleaned = cv2.morphologyEx(chip_mask_filled, cv2.MORPH_CLOSE, kernel_large)
        chip_mask_cleaned = cv2.morphologyEx(chip_mask_cleaned, cv2.MORPH_OPEN, kernel_large)
        
        # Find the largest connected component (the chip)
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(chip_mask_cleaned)
        if num_labels > 1:
            # Get the largest component (excluding background)
            areas = stats[1:, cv2.CC_STAT_AREA]
            largest_idx = 1 + np.argmax(areas)
            
            # Only accept if it's significantly large (at least 20% of image)
            if areas[largest_idx - 1] > 0.2 * h * w:
                chip_mask = (labels == largest_idx).astype(np.uint8) * 255
            else:
                # If no large component, assume entire image is chip
                chip_mask = np.ones((h, w), np.uint8) * 255
        else:
            chip_mask = chip_mask_cleaned
        
        # Debug visualization
        if self.debug:
            debug_img = img.copy()
            # Draw detected edges in green
            contours, _ = cv2.findContours(chip_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)
            
            # Save intermediate steps
            cv2.imwrite(f"edge_debug_edges_{time.time():.0f}.png", edges)
            cv2.imwrite(f"edge_debug_background_{time.time():.0f}.png", background_mask if len(img.shape) == 3 else np.zeros_like(gray))
            cv2.imwrite(f"edge_debug_chip_mask_{time.time():.0f}.png", chip_mask)
            cv2.imwrite(f"edge_debug_final_{time.time():.0f}.png", debug_img)
        
        return chip_mask
    
    def find_chip_boundaries(self, mask):
        """Find the boundaries of the chip from the mask"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None
        
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        return x, x + w, y, y + h
    
    def is_on_chip(self, img, x, y, margin=10):
        """
        Check if a specific pixel position is on the chip
        margin: pixels from edge to consider as boundary
        """
        mask = self.detect_chip_edges(img)
        h, w = mask.shape
        
        # Check bounds
        if x < 0 or x >= w or y < 0 or y >= h:
            return False
        
        # Check if pixel is on chip (with margin)
        # For edge detection, we'll check a small region around the point
        x_start = max(0, x - margin)
        x_end = min(w, x + margin + 1)
        y_start = max(0, y - margin)
        y_end = min(h, y + margin + 1)
        
        # If the majority of pixels in the margin area are on-chip, consider it on-chip
        region = mask[y_start:y_end, x_start:x_end]
        on_chip_pixels = np.sum(region > 0)
        total_pixels = region.size
        
        # Require at least 80% of pixels to be on-chip
        return on_chip_pixels > 0.8 * total_pixels


class NoEdgeDetector(EdgeDetector):
    """Dummy edge detector that assumes entire image is chip (for debugging)"""
    
    def __init__(self, debug=False):
        super().__init__(debug=debug)
        
    def detect_chip_edges(self, img):
        """Always return full image as chip"""
        h, w = img.shape[:2]
        return np.ones((h, w), np.uint8) * 255
    
    def is_on_chip(self, img, x, y, margin=10):
        """Always return True (entire image is chip)"""
        h, w = img.shape[:2]
        return 0 <= x < w and 0 <= y < h


class BackgroundEdgeDetector(EdgeDetector):
    """Edge detector that focuses on background (beige/yellow tape) detection"""
    
    def __init__(self, debug=False):
        super().__init__(debug=debug)
        
    def detect_chip_edges(self, img):
        """
        Detect chip by identifying background (beige/yellow) areas
        """
        if len(img.shape) != 3:
            # Need color image for background detection
            return np.ones(img.shape[:2], np.uint8) * 255
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # Define multiple ranges for beige/yellow background
        # Range 1: Typical beige
        lower1 = np.array([20, 30, 120])
        upper1 = np.array([35, 100, 255])
        mask1 = cv2.inRange(hsv, lower1, upper1)
        
        # Range 2: Lighter beige/yellow
        lower2 = np.array([15, 20, 150])
        upper2 = np.array([40, 80, 255])
        mask2 = cv2.inRange(hsv, lower2, upper2)
        
        # Combine background masks
        background_mask = cv2.bitwise_or(mask1, mask2)
        
        # Clean up background mask
        kernel = np.ones((5, 5), np.uint8)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_OPEN, kernel)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_CLOSE, kernel)
        
        # Chip is NOT background
        chip_mask = cv2.bitwise_not(background_mask)
        
        # Fill small holes
        kernel_large = np.ones((30, 30), np.uint8)
        chip_mask = cv2.morphologyEx(chip_mask, cv2.MORPH_CLOSE, kernel_large)
        
        # Keep only large connected components
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(chip_mask)
        h, w = chip_mask.shape
        min_area = 0.1 * h * w  # At least 10% of image
        
        final_mask = np.zeros_like(chip_mask)
        for i in range(1, num_labels):
            if stats[i, cv2.CC_STAT_AREA] > min_area:
                final_mask[labels == i] = 255
        
        # If no large components, assume entire image is chip
        if np.sum(final_mask) == 0:
            final_mask = np.ones((h, w), np.uint8) * 255
        
        if self.debug:
            debug_img = img.copy()
            contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)
            cv2.imwrite(f"background_debug_{time.time():.0f}.png", debug_img)
            cv2.imwrite(f"background_mask_{time.time():.0f}.png", background_mask)
        
        return final_mask


# --- Scanner Worker Thread ---
class ScanWorker(QThread):
    progress = pyqtSignal(int, int)
    finished = pyqtSignal(str)
    status = pyqtSignal(str)
    
    def __init__(self, mode, x_steps, y_steps, out_csv, region, 
                 edge_margin=20, debug=False, edge_method='background'):
        super().__init__()
        self.mode = mode
        self.x_steps = x_steps
        self.y_steps = y_steps
        self.out_csv = out_csv
        self.region = region
        self.edge_margin = edge_margin
        self.stage = StageController()
        
        if mode == 'adaptive':
            if edge_method == 'background':
                self.edge_detector = BackgroundEdgeDetector(debug=debug)
            elif edge_method == 'none':
                self.edge_detector = NoEdgeDetector(debug=debug)
            else:
                self.edge_detector = EdgeDetector(debug=debug)
        else:
            self.edge_detector = None
            
        self.current_origin_x = 0
        self.current_origin_y = 0
        
    def process_position(self, img, step_id, step_x, step_y, off_x_um, off_y_um, writer):
        """Process current position - detect flakes and save data"""
        result = detection_client.infer(img, model_id="raman-fgrub/4")
        data = result if isinstance(result, dict) else result.json()
        
        svd = sv.Detections.from_inference(result)
        svd = svd[np.isin(svd.class_id, list(selected_classes))]
        annotated = polygon_annotator.annotate(scene=img.copy(), detections=svd)
        annotated = label_annotator.annotate(scene=annotated, detections=svd)
        png_path = f"annot_{step_id}.png"
        cv2.imwrite(png_path, annotated)
        
        h, w, _ = img.shape
        origin_x = w // 2
        origin_y = h // 2
        
        um_per_pixel_vert = STEP_X_UM / h
        um_per_pixel_horiz = STEP_Y_UM / w
        
        flakes_found = 0
        for pred in data.get('predictions', []):
            if pred['class_id'] in selected_classes:
                cx, cy = pred['x'], pred['y']
                pts = [(pt['x'], pt['y']) for pt in pred['points']]
                
                dx = cx - origin_x
                dy = cy - origin_y
                
                real_x = off_x_um - dy * um_per_pixel_vert
                real_y = off_y_um - dx * um_per_pixel_horiz
                
                writer.writerow({
                    'step_id': step_id,
                    'step_x': step_x,
                    'step_y': step_y,
                    'pix_origin_x': origin_x,
                    'pix_origin_y': origin_y,
                    'center_x': cx,
                    'center_y': cy,
                    'points': pts,
                    'real_x_um': real_x,
                    'real_y_um': real_y,
                    'class': pred['class'],
                    'detection_id': pred['detection_id']
                })
                flakes_found += 1
        
        return flakes_found
    
    def run_grid_scan(self):
        """Grid scanning method"""
        total = (self.x_steps + 1) * (self.y_steps + 1)
        count = 0
        
        self.status.emit("Starting grid scan...")
        
        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um', 
                'class', 'detection_id'
            ])
            writer.writeheader()
            
            for j in range(self.x_steps + 1):
                for k in range(self.y_steps + 1):
                    sid = f"{j}-{k}"
                    
                    off_y_um = -j * STEP_Y_UM
                    off_x_um = -k * STEP_X_UM
                    
                    self.status.emit(f"Moving to grid({j},{k})...")
                    self.stage.move_absolute(off_y_um, off_x_um)
                    time.sleep(1)
                    
                    with mss.mss() as sct:
                        shot = sct.grab(self.region)
                        img = np.array(shot)
                    
                    flakes = self.process_position(img, sid, j, k, off_x_um, off_y_um, writer)
                    
                    count += 1
                    self.progress.emit(count, total)
                    self.status.emit(f"Grid({j},{k}): Found {flakes} flakes")
    
    def find_upper_left_corner(self):
        """Find the upper-left corner of the chip"""
        self.status.emit("Finding chip upper-left corner...")
        
        if isinstance(self.edge_detector, NoEdgeDetector):
            self.status.emit("No edge detection - using current position as origin")
            self.current_origin_x = 0
            self.current_origin_y = 0
            return 0, 0
        
        with mss.mss() as sct:
            shot = sct.grab(self.region)
            img = np.array(shot)
        
        h, w, _ = img.shape
        center_x, center_y = w // 2, h // 2
        
        if not self.edge_detector.is_on_chip(img, center_x, center_y):
            self.status.emit("Not on chip! Please move to chip first.")
            return None
        
        # Find left edge (moving in +Y direction)
        self.status.emit("Finding left edge...")
        current_y = 0
        while True:
            with mss.mss() as sct:
                img = np.array(sct.grab(self.region))
            
            left_check = int(w * 0.2)
            if not self.edge_detector.is_on_chip(img, left_check, center_y):
                self.status.emit("Found left edge")
                current_y -= STEP_Y_UM * 0.5
                self.stage.move_absolute(current_y, 0)
                time.sleep(1)
                break
            
            current_y += STEP_Y_UM * 0.5
            self.stage.move_absolute(current_y, 0)
            time.sleep(0.5)
        
        # Find top edge (moving in +X direction)
        self.status.emit("Finding top edge...")
        current_x = 0
        while True:
            with mss.mss() as sct:
                img = np.array(sct.grab(self.region))
            
            top_check = int(h * 0.2)
            if not self.edge_detector.is_on_chip(img, center_x, top_check):
                self.status.emit("Found top edge")
                current_x -= STEP_X_UM * 0.5
                self.stage.move_absolute(current_y, current_x)
                time.sleep(1)
                break
            
            current_x += STEP_X_UM * 0.5
            self.stage.move_absolute(current_y, current_x)
            time.sleep(0.5)
        
        self.status.emit(f"Starting position: ({current_x:.1f}, {current_y:.1f}) μm")
        self.current_origin_x = current_x
        self.current_origin_y = current_y
        return current_x, current_y
    
    def run_adaptive_scan(self):
        """Adaptive scanning that follows chip edges"""
        start = self.find_upper_left_corner()
        if start is None:
            return
        
        self.status.emit("Starting adaptive scan...")
        
        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um', 
                'class', 'detection_id'
            ])
            writer.writeheader()
            
            row_num = 0
            total_positions = 0
            
            while True:
                self.status.emit(f"\n=== Starting Row {row_num} ===")
                
                if row_num > 0:
                    current_x = self.current_origin_x - row_num * STEP_X_UM
                    self.stage.move_absolute(self.current_origin_y, current_x)
                    time.sleep(1)
                    
                    with mss.mss() as sct:
                        img = np.array(sct.grab(self.region))
                    h, w, _ = img.shape
                    if not self.edge_detector.is_on_chip(img, w//2, h//2):
                        self.status.emit(f"Reached bottom edge of chip at row {row_num}")
                        break
                    
                    if not isinstance(self.edge_detector, NoEdgeDetector):
                        self.status.emit(f"Finding left edge for row {row_num}")
                        current_y = self.current_origin_y
                        while True:
                            with mss.mss() as sct:
                                img = np.array(sct.grab(self.region))
                            
                            if self.edge_detector.is_on_chip(img, w//2, h//2):
                                current_y -= self.edge_margin * (STEP_Y_UM / w)
                                self.stage.move_absolute(current_y, current_x)
                                time.sleep(0.5)
                                break
                            
                            current_y += STEP_Y_UM * 0.25
                            self.stage.move_absolute(current_y, current_x)
                            time.sleep(0.5)
                    else:
                        current_y = self.current_origin_y
                else:
                    current_x = self.current_origin_x
                    current_y = self.current_origin_y
                
                col_num = 0
                self.status.emit(f"Scanning row {row_num} rightward...")
                
                while True:
                    with mss.mss() as sct:
                        shot = sct.grab(self.region)
                        img = np.array(shot)
                    
                    h, w, _ = img.shape
                    
                    right_check = int(w * 0.8)
                    if not self.edge_detector.is_on_chip(img, right_check, h//2):
                        self.status.emit(f"Row {row_num}: Reached right edge at column {col_num}")
                        break
                    
                    if isinstance(self.edge_detector, NoEdgeDetector) and col_num >= self.y_steps:
                        self.status.emit(f"Row {row_num}: Reached column limit ({self.y_steps})")
                        break
                    
                    sid = f"{row_num}-{col_num}"
                    flakes = self.process_position(img, sid, row_num, col_num, 
                                                 current_x, current_y, writer)
                    
                    total_positions += 1
                    self.progress.emit(total_positions, total_positions + 10)
                    self.status.emit(f"Position ({row_num},{col_num}): Found {flakes} flakes")
                    
                    col_num += 1
                    current_y -= STEP_Y_UM
                    self.stage.move_absolute(current_y, current_x)
                    time.sleep(1)
                
                self.status.emit(f"Row {row_num} complete: {col_num} positions scanned")
                
                row_num += 1
                
                if row_num > 100:
                    self.status.emit("Safety limit reached (100 rows)")
                    break
                
                if isinstance(self.edge_detector, NoEdgeDetector) and row_num >= self.x_steps:
                    self.status.emit(f"Reached row limit ({self.x_steps} rows)")
                    break
        
        self.status.emit(f"\n=== Scan Complete ===")
        self.status.emit(f"Total positions: {total_positions}")
        self.status.emit(f"Total rows: {row_num}")
    
    def run(self):
        """Main run method"""
        try:
            if self.mode == 'grid':
                self.run_grid_scan()
            else:
                self.run_adaptive_scan()
            
            self.stage.close()
            self.finished.emit(self.out_csv)
        except Exception as e:
            self.status.emit(f"Error: {str(e)}")
            self.stage.close()
            self.finished.emit("")


# --- UI Components ---
class RotationHandle(QGraphicsEllipseItem):
    def __init__(self, parent, uid, win):
        super().__init__(-5, -5, 20, 20, parent)
        self.setBrush(QBrush(QColor('black')))
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        self.uid = uid
        self.win = win
        self.parent = parent
    
    def itemChange(self, chg, val):
        if chg == QGraphicsEllipseItem.GraphicsItemChange.ItemPositionHasChanged:
            c = self.parent.boundingRect().center()
            dx = self.pos().x() - c.x()
            dy = self.pos().y() - c.y()
            ang = math.degrees(math.atan2(dy, dx))
            self.parent.setTransform(
                QTransform()
                .translate(c.x(), c.y())
                .rotate(ang)
                .translate(-c.x(), -c.y())
            )
            self.win.flakes[self.uid]['rotation'] = ang
        return super().itemChange(chg, val)


class DraggablePolygon(QGraphicsPolygonItem):
    def __init__(self, poly, uid, win):
        super().__init__(poly)
        self.uid = uid
        self.win = win
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        rh = RotationHandle(self, uid, win)
        rh.setPos(self.boundingRect().topRight())
    
    def itemChange(self, chg, val):
        if chg == QGraphicsPolygonItem.GraphicsItemChange.ItemPositionHasChanged:
            pos = val
            base = self.win.flakes[self.uid]
            base['center_px'] = (base['center_x'] + pos.x(), base['center_y'] + pos.y())
        return super().itemChange(chg, val)


# --- Flake Selector with Alignment ---
class FlakeSelector(QMainWindow):
    def __init__(self, csv_path):
        super().__init__()
        self.flakes = {}
        self.polygon_items = {}
        self.screenshot_map = {}
        self.flake_objects = []
        self.chip_aligner = None
        self.transformer = None
        self.init_ui()
        self.load_csv(csv_path)

    def init_ui(self):
        self.setWindowTitle('Flake Selector')
        self.resize(1200, 800)
        
        # Toolbar for alignment functions
        toolbar = self.addToolBar('Alignment')
        
        save_ref_action = toolbar.addAction('Save as Reference')
        save_ref_action.triggered.connect(self.save_reference)
        
        align_action = toolbar.addAction('Load & Align')
        align_action.triggered.connect(self.load_and_align)
        
        select_locators_action = toolbar.addAction('Select Locators')
        select_locators_action.triggered.connect(self.select_locator_flakes)
        
        toolbar.addSeparator()
        
        self.alignment_status = QLabel('No alignment')
        toolbar.addWidget(self.alignment_status)
        
        # Left pane
        self.all_list = QListWidget()
        self.all_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.all_list.itemSelectionChanged.connect(self.update_selected_flakes)
        self.sel_list = QListWidget()
        self.sel_list.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.sel_list.itemClicked.connect(self.display_screenshot)
        self.process_button = QPushButton('Confirm Selection')
        self.process_button.clicked.connect(self.goto)
        self.process_button.setEnabled(False)
        
        self.use_transformed_coords = QCheckBox('Use Aligned Coordinates')
        self.use_transformed_coords.setEnabled(False)
        
        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel('Detected Flakes'))
        left_layout.addWidget(self.all_list)
        left_layout.addWidget(QLabel('Order'))
        left_layout.addWidget(self.sel_list)
        left_layout.addWidget(self.use_transformed_coords)
        left_layout.addWidget(self.process_button)
        left_widget = QWidget()
        left_widget.setLayout(left_layout)

        # Right pane
        self.flake_view = QGraphicsView()
        self.flake_scene = QGraphicsScene()
        self.flake_view.setScene(self.flake_scene)
        self.flake_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        right_splitter = QSplitter(Qt.Orientation.Vertical)
        right_splitter.addWidget(self.flake_view)
        right_splitter.addWidget(self.image_label)
        right_splitter.setStretchFactor(0, 1)
        right_splitter.setStretchFactor(1, 1)
        right_splitter.setSizes([400, 400])

        main_split = QSplitter(Qt.Orientation.Horizontal)
        main_split.addWidget(left_widget)
        main_split.addWidget(right_splitter)
        main_split.setStretchFactor(1, 1)
        self.setCentralWidget(main_split)

    def load_csv(self, path):
        self.flake_objects = []
        with open(path) as f:
            for r in csv.DictReader(f):
                sid = r['step_id']
                img_path = f"annot_{sid}.png"
                self.screenshot_map[sid] = img_path
                
                cx = float(r['center_x'])
                cy = float(r['center_y'])
                pts = ast.literal_eval(r['points'])
                cl = r['class']
                rx = float(r['real_x_um'])
                ry = float(r['real_y_um'])
                
                pix_origin_x = float(r['pix_origin_x'])
                pix_origin_y = float(r['pix_origin_y'])
                
                uid = f"{sid}_{r['detection_id']}"
                self.flakes[uid] = {
                    'step_id': sid,
                    'center_x': cx,
                    'center_y': cy,
                    'shape': pts,
                    'class': cl,
                    'real_x_um': rx,
                    'real_y_um': ry,
                    'rotation': 0,
                    'pix_origin_x': pix_origin_x,
                    'pix_origin_y': pix_origin_y,
                    'transformed_x_um': None,
                    'transformed_y_um': None
                }
                self.all_list.addItem(uid)
                
                flake_obj = Flake(
                    id=uid,
                    center_x=cx,
                    center_y=cy,
                    real_x_um=rx,
                    real_y_um=ry,
                    shape=pts,
                    class_name=cl
                )
                self.flake_objects.append(flake_obj)

    def save_reference(self):
        """Save current chip as reference for future alignment"""
        chip_id, ok = QInputDialog.getText(self, 'Save Reference', 'Enter chip ID:')
        if not ok or not chip_id:
            return
        
        filepath, _ = QFileDialog.getSaveFileName(
            self, 'Save Reference Data', f'{chip_id}_reference.json', 'JSON Files (*.json)')
        if not filepath:
            return
        
        selected_items = self.all_list.selectedItems()
        if len(selected_items) >= 3:
            locator_ids = [item.text() for item in selected_items]
            locator_flakes = [f for f in self.flake_objects if f.id in locator_ids]
        else:
            aligner = ChipAligner()
            locator_flakes = aligner.select_locator_flakes(self.flake_objects, n_locators=5)
        
        save_chip_reference(chip_id, self.flake_objects, locator_flakes, filepath,
                           edge_detection_method="BackgroundEdgeDetector")
        
        for flake in locator_flakes:
            items = self.all_list.findItems(flake.id, Qt.MatchFlag.MatchExactly)
            if items:
                items[0].setBackground(QColor(200, 255, 200))
        
        QMessageBox.information(self, 'Reference Saved', 
                              f'Saved reference with {len(locator_flakes)} locator flakes')

    def load_and_align(self):
        """Load reference and align current chip"""
        filepath, _ = QFileDialog.getOpenFileName(
            self, 'Load Reference Data', '', 'JSON Files (*.json)')
        if not filepath:
            return
        
        try:
            ref_flakes, locator_ids, edge_detection_method = load_chip_reference(filepath)
            # Note: edge_detection_method is available but not used in this legacy UI
            locator_flakes = [f for f in ref_flakes if f.id in locator_ids]
            
            self.chip_aligner = ChipAligner()
            result = self.chip_aligner.align_chips(
                ref_flakes, self.flake_objects, locator_flakes)
            
            if result['success']:
                self.transformer = result['transformer']
                transform = result['transform']
                
                for flake in self.flakes.values():
                    orig_pt = np.array([flake['real_x_um'], flake['real_y_um']])
                    trans_pt = self.transformer.inverse_transform_point(orig_pt)
                    flake['transformed_x_um'] = trans_pt[0]
                    flake['transformed_y_um'] = trans_pt[1]
                
                self.use_transformed_coords.setEnabled(True)
                self.use_transformed_coords.setChecked(True)
                self.alignment_status.setText(
                    f"Aligned: T=[{transform['translation'][0]:.1f}, "
                    f"{transform['translation'][1]:.1f}] μm, "
                    f"R={transform['rotation_degrees']:.1f}°, "
                    f"Error={result['mean_error']:.1f} μm")
                
                for match in result['matches']:
                    if match['is_inlier']:
                        items = self.all_list.findItems(
                            match['current_id'], Qt.MatchFlag.MatchExactly)
                        if items:
                            items[0].setBackground(QColor(200, 200, 255))
                
                QMessageBox.information(self, 'Alignment Success',
                    f"Aligned with {result['num_inliers']} inliers\n"
                    f"Mean error: {result['mean_error']:.1f} μm\n"
                    f"Rotation: {transform['rotation_degrees']:.1f}°")
            else:
                QMessageBox.warning(self, 'Alignment Failed', result['error'])
                
        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Failed to load/align: {str(e)}')

    def select_locator_flakes(self):
        """Manually select locator flakes for better alignment"""
        selected_items = self.all_list.selectedItems()
        if len(selected_items) < 3:
            QMessageBox.warning(self, 'Insufficient Selection', 
                              'Please select at least 3 flakes as locators')
            return
        
        for item in self.all_list.findItems('*', Qt.MatchFlag.MatchWildcard):
            item.setBackground(QColor())
        
        for item in selected_items:
            item.setBackground(QColor(255, 255, 200))
        
        QMessageBox.information(self, 'Locators Selected',
                              f'Selected {len(selected_items)} locator flakes')

    def goto(self):
        """Navigate to selected flakes using original or transformed coordinates"""
        st = StageController()
        use_transformed = (self.use_transformed_coords.isChecked() and 
                         self.transformer is not None)
        
        for i in range(self.sel_list.count()):
            uid = self.sel_list.item(i).text()
            f = self.flakes[uid]
            
            if use_transformed and f['transformed_x_um'] is not None:
                x_um = f['transformed_x_um']
                y_um = f['transformed_y_um']
            else:
                x_um = f['real_x_um']
                y_um = f['real_y_um']
            
            st.move_absolute(y_um, x_um)  # Y first, X second
            time.sleep(0.5)
        st.close()

    def update_selected_flakes(self):
        uids = [item.text() for item in self.all_list.selectedItems()]
        uids.sort()
        
        self.sel_list.clear()
        for uid in uids:
            self.sel_list.addItem(uid)
        self.process_button.setEnabled(bool(uids))
        
        self.flake_scene.clear()
        materials = list({f['class'] for f in self.flakes.values()})
        colors = [QColor('red'), QColor('blue'), QColor('green'), QColor('yellow')]
        color_map = {cls: colors[i % len(colors)] for i, cls in enumerate(materials)}
        
        for uid in uids:
            f = self.flakes[uid]
            poly = QPolygonF([QPointF(x, y) for x, y in f['shape']])
            item = DraggablePolygon(poly, uid, self)
            item.setPen(QPen(Qt.GlobalColor.black))
            col = color_map.get(f['class'], QColor('gray'))
            col.setAlpha(100)
            item.setBrush(QBrush(col))
            self.flake_scene.addItem(item)
            self.polygon_items[uid] = item
            
            rot = f['rotation']
            ctr_pt = item.boundingRect().center()
            item.setTransform(
                QTransform()
                .translate(ctr_pt.x(), ctr_pt.y())
                .rotate(rot)
                .translate(-ctr_pt.x(), -ctr_pt.y())
            )
        
        if uids:
            self.flake_view.fitInView(
                self.flake_scene.itemsBoundingRect(), 
                Qt.AspectRatioMode.KeepAspectRatio
            )

    def display_screenshot(self, item):
        uid = item.text()
        sid = self.flakes[uid]['step_id']
        img_path = self.screenshot_map.get(sid)
        if img_path and os.path.exists(img_path):
            pix = QPixmap(img_path)
            self.image_label.setPixmap(pix.scaled(
                self.image_label.size(), 
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))


# --- Main Application ---
class MainApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('Scan & Select with Auto-Alignment')
        
        # Scan controls
        scan_layout = QHBoxLayout()
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(['Grid Scan', 'Adaptive Chip Scan'])
        scan_layout.addWidget(QLabel('Mode:'))
        scan_layout.addWidget(self.mode_combo)
        
        btn = QPushButton('Start Scan')
        btn.clicked.connect(self.scan)
        scan_layout.addWidget(btn)
        
        test_btn = QPushButton('Test Edge Detection')
        test_btn.clicked.connect(self.test_edge_detection)
        scan_layout.addWidget(test_btn)

        test_align_btn = QPushButton('Test Alignment')
        test_align_btn.clicked.connect(self.test_alignment)
        scan_layout.addWidget(test_align_btn)
        
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(['Background Detection', 'General Detection', 'No Edge Detection'])
        scan_layout.addWidget(QLabel('Edge Method:'))
        scan_layout.addWidget(self.edge_method_combo)
        
        # Progress and status
        self.pb = QProgressBar()
        self.lb = QLabel('Ready')
        
        # Main layout
        v = QVBoxLayout()
        v.addLayout(scan_layout)
        v.addWidget(self.pb)
        v.addWidget(self.lb)
        
        w = QWidget()
        w.setLayout(v)
        self.setCentralWidget(w)
    
    def test_edge_detection(self):
        """Test edge detection on current view"""
        region = {
            'top': 245,
            'left': 484,
            'width': 1893-484,
            'height': 1247-245
        }
        
        with mss.mss() as sct:
            shot = sct.grab(region)
            img = np.array(shot)
        
        methods = [
            ('General', EdgeDetector(debug=True)),
            ('Background', BackgroundEdgeDetector(debug=True)),
            ('No Edge', NoEdgeDetector(debug=True))
        ]
        
        results = []
        for method_name, detector in methods:
            mask = detector.detect_chip_edges(img)
            
            h, w = mask.shape
            center_x, center_y = w // 2, h // 2
            
            points_to_check = [
                (center_x, center_y, "Center"),
                (int(w * 0.2), center_y, "Left 20%"),
                (int(w * 0.8), center_y, "Right 80%"),
                (center_x, int(h * 0.2), "Top 20%"),
                (center_x, int(h * 0.8), "Bottom 80%")
            ]
            
            status = f"\n{method_name} Method:\n"
            for x, y, label in points_to_check:
                on_chip = detector.is_on_chip(img, x, y)
                status += f"  {label}: {'ON chip' if on_chip else 'OFF chip'}\n"
            
            results.append(status)
            
            vis_img = img.copy()
            for x, y, label in points_to_check:
                color = (0, 255, 0) if detector.is_on_chip(img, x, y) else (0, 0, 255)
                cv2.circle(vis_img, (x, y), 10, color, -1)
                cv2.putText(vis_img, label, (x-50, y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(vis_img, contours, -1, (255, 255, 0), 2)
            
            cv2.imwrite(f"edge_test_{method_name.lower()}_visualization.png", vis_img)
            cv2.imwrite(f"edge_test_{method_name.lower()}_mask.png", mask)
        
        combined_status = "Edge Detection Test Results:" + ''.join(results)
        combined_status += f"\nSaved visualizations for all methods"
        
        self.lb.setText(combined_status)
        QMessageBox.information(self, "Edge Detection Test", combined_status)

    def test_alignment(self):
        """Test alignment between two scans"""
        ref_file, _ = QFileDialog.getOpenFileName(
            self, 'Select Reference Scan', '', 'CSV Files (*.csv)')
        if not ref_file:
            return
            
        curr_file, _ = QFileDialog.getOpenFileName(
            self, 'Select Current Scan', '', 'CSV Files (*.csv)')
        if not curr_file:
            return
        
        ref_flakes = []
        with open(ref_file) as f:
            for r in csv.DictReader(f):
                flake = Flake(
                    id=f"{r['step_id']}_{r['detection_id']}",
                    center_x=float(r['center_x']),
                    center_y=float(r['center_y']),
                    real_x_um=float(r['real_x_um']),
                    real_y_um=float(r['real_y_um']),
                    shape=ast.literal_eval(r['points']),
                    class_name=r['class']
                )
                ref_flakes.append(flake)
        
        curr_flakes = []
        with open(curr_file) as f:
            for r in csv.DictReader(f):
                flake = Flake(
                    id=f"{r['step_id']}_{r['detection_id']}",
                    center_x=float(r['center_x']),
                    center_y=float(r['center_y']),
                    real_x_um=float(r['real_x_um']),
                    real_y_um=float(r['real_y_um']),
                    shape=ast.literal_eval(r['points']),
                    class_name=r['class']
                )
                curr_flakes.append(flake)
        
        aligner = ChipAligner()
        locators = aligner.select_locator_flakes(ref_flakes, n_locators=5)
        result = aligner.align_chips(ref_flakes, curr_flakes, locators)
        
        if result['success']:
            transform = result['transform']
            msg = (f"Alignment successful!\n"
                   f"Translation: [{transform['translation'][0]:.1f}, "
                   f"{transform['translation'][1]:.1f}] μm\n"
                   f"Rotation: {transform['rotation_degrees']:.1f}°\n"
                   f"Scale: {transform['scale']:.3f}\n"
                   f"Matches: {result['num_inliers']}\n"
                   f"Mean error: {result['mean_error']:.1f} μm")
            QMessageBox.information(self, 'Alignment Test', msg)
        else:
            QMessageBox.warning(self, 'Alignment Failed', result['error'])
        
    def scan(self):
        mode = 'adaptive' if self.mode_combo.currentIndex() == 1 else 'grid'
        
        if mode == 'grid':
            xs, ok1 = QInputDialog.getInt(self, 'X Steps', 'Number of X steps:', 0, 0)
            ys, ok2 = QInputDialog.getInt(self, 'Y Steps', 'Number of Y steps:', 0, 0)
            if not(ok1 and ok2):
                return
        else:
            edge_idx = self.edge_method_combo.currentIndex()
            if edge_idx == 2:  # No Edge Detection
                xs, ok1 = QInputDialog.getInt(self, 'Rows', 'Number of rows to scan:', 10, 1, 100)
                ys, ok2 = QInputDialog.getInt(self, 'Columns', 'Number of columns per row:', 10, 1, 100)
                if not(ok1 and ok2):
                    return
            else:
                xs, ys = 0, 0
                
            margin, ok = QInputDialog.getInt(self, 'Edge Margin', 
                                           'Pixels from edge to stop:', 20, 5, 100)
            if not ok:
                return
        
        region = {
            'top': 245,
            'left': 484,
            'width': 1893-484,
            'height': 1247-245
        }
        
        out, _ = QFileDialog.getSaveFileName(self, 'Save CSV', '', '*.csv')
        if not out:
            return
        
        if mode == 'adaptive':
            edge_idx = self.edge_method_combo.currentIndex()
            if edge_idx == 0:
                edge_method = 'background'
            elif edge_idx == 1:
                edge_method = 'general'
            else:
                edge_method = 'none'
                
            self.worker = ScanWorker(mode, xs, ys, out, region, 
                                   edge_margin=margin, debug=False, edge_method=edge_method)
        else:
            self.worker = ScanWorker(mode, xs, ys, out, region)
            
        self.worker.progress.connect(lambda v, t: self.pb.setValue(int(v/t*100)))
        self.worker.status.connect(lambda msg: self.lb.setText(msg))
        self.worker.finished.connect(self.show_selector)
        self.worker.start()
        
        self.lb.setText(f'Starting {mode} scan...')
        
    def show_selector(self, path):
        if path:
            self.lb.setText('Scan complete!')
            self.selector = FlakeSelector(path)
            self.selector.show()
        else:
            self.lb.setText('Scan failed!')
            QMessageBox.warning(self, 'Scan Failed', 
                              'The scan failed. Check the console for errors.')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = MainApp()
    win.show()
    sys.exit(app.exec())