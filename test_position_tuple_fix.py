#!/usr/bin/env python3
"""
Test script to verify that the position tuple fix works correctly.
This tests that the corner detection correctly handles the (y, x) tuple format.
"""

import sys
from unittest.mock import Mock

# Import the functions
from chip_alignment import ChipAlignmentSystem
from edge_detection import BackgroundEdgeDetector

def test_position_tuple_handling():
    """Test that the corner detection correctly handles position tuples."""
    print("=== Testing Position Tuple Handling ===")
    
    try:
        # Create mock stage controller that returns (y, x) tuples
        mock_stage = Mock()
        mock_stage.set_zero = Mock()
        mock_stage.move_absolute = Mock()
        
        # Mock get_position to return (y, x) tuple like the real StageController
        position_calls = []
        def mock_get_position():
            call_count = len(position_calls)
            position_calls.append(call_count)
            
            # Return different positions to simulate movement
            if call_count == 0:
                return (150.0, 100.0)  # Initial position: y=150, x=100
            elif call_count == 1:
                return (155.0, 100.0)  # After first movement: y=155, x=100
            else:
                return (160.0, 105.0)  # Final position: y=160, x=105
        
        mock_stage.get_position = Mock(side_effect=mock_get_position)
        
        # Create mock edge detector
        edge_detector = BackgroundEdgeDetector(debug=False)
        
        # Mock is_on_chip to find edges quickly
        call_count = [0]
        def mock_is_on_chip(img, x, y, margin=20):
            call_count[0] += 1
            # Find edge after 2 calls
            return call_count[0] < 2
        
        edge_detector.is_on_chip = Mock(side_effect=mock_is_on_chip)
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        print("\n--- Testing Corner Detection with Position Tuples ---")
        
        # Test corner detection
        corner_coords = alignment_system._find_current_upper_left_corner(edge_detector)
        
        # Verify the results
        print(f"\n--- Verifying Position Tuple Handling ---")
        
        if corner_coords is not None:
            print(f"✅ Corner detection succeeded: {corner_coords}")
            
            # Verify that get_position was called
            if mock_stage.get_position.call_count > 0:
                print(f"✅ get_position() was called {mock_stage.get_position.call_count} times")
            else:
                print(f"❌ get_position() was not called")
                return False
            
            # Verify the final coordinates are reasonable
            final_x, final_y = corner_coords
            if isinstance(final_x, (int, float)) and isinstance(final_y, (int, float)):
                print(f"✅ Final coordinates are valid numbers: x={final_x}, y={final_y}")
            else:
                print(f"❌ Final coordinates are not valid: x={final_x}, y={final_y}")
                return False
            
            # Verify no tuple indexing errors occurred
            print(f"✅ No 'tuple indices must be integers or slices, not str' errors")
            
        else:
            print(f"❌ Corner detection failed (returned None)")
            return False
        
        print(f"\n🎉 Position tuple handling works correctly!")
        return True
        
    except Exception as e:
        error_str = str(e)
        if "tuple indices must be integers or slices, not str" in error_str:
            print(f"❌ Still has tuple indexing error: {error_str}")
            return False
        else:
            print(f"❌ Test failed with different error: {error_str}")
            import traceback
            traceback.print_exc()
            return False

def test_realignment_with_position_tuples():
    """Test that the full re-alignment workflow works with position tuples."""
    print(f"\n=== Testing Full Re-alignment with Position Tuples ===")
    
    try:
        # Create mock stage
        mock_stage = Mock()
        mock_stage.set_zero = Mock()
        mock_stage.move_absolute = Mock()
        
        # Mock get_position to return (y, x) tuples
        mock_stage.get_position.return_value = (150.0, 100.0)  # y=150, x=100
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        # Mock the corner detection to return a specific result
        def mock_find_corner(edge_detector):
            print(f"  Corner detection using position tuples")
            return (105.0, 155.0)  # Return found corner
        
        alignment_system._find_current_upper_left_corner = mock_find_corner
        
        # Create test reference data
        reference_data = {
            'boundary_reference': {'chip_origin_abs': [100.0, 150.0]},
            'relative_flakes': [
                {'id': 1, 'center_x': 50, 'center_y': 75, 'real_x_um_rel': 50.0, 'real_y_um_rel': 75.0}
            ],
            'edge_detection_method': 'BackgroundEdgeDetector'
        }
        
        print("\n--- Testing Full Re-alignment Workflow ---")
        
        # Perform re-alignment
        result = alignment_system.perform_realignment(reference_data)
        
        if result.get('success'):
            print(f"✅ Re-alignment completed successfully")
            print(f"  Translation: {result.get('transformation', {}).get('translation', 'N/A')}")
            print(f"  Transformed flakes: {len(result.get('transformed_flakes', []))}")
            print(f"✅ No position tuple errors in full workflow")
        else:
            error_msg = result.get('error', 'Unknown error')
            if "tuple indices must be integers or slices, not str" in error_msg:
                print(f"❌ Re-alignment has tuple indexing error: {error_msg}")
                return False
            else:
                print(f"⚠️ Re-alignment failed with different error: {error_msg}")
                print(f"  But no tuple indexing errors (which is what we're testing)")
        
        print(f"\n🎉 Full re-alignment workflow handles position tuples correctly!")
        return True
        
    except Exception as e:
        error_str = str(e)
        if "tuple indices must be integers or slices, not str" in error_str:
            print(f"❌ Re-alignment crashed with tuple indexing error: {error_str}")
            return False
        else:
            print(f"⚠️ Re-alignment crashed with different error: {error_str}")
            print(f"  But no tuple indexing errors (which is what we're testing)")
            return True

def test_position_format_consistency():
    """Test that position handling is consistent throughout the code."""
    print(f"\n=== Testing Position Format Consistency ===")
    
    try:
        # Test different position scenarios
        test_positions = [
            (100.0, 150.0),   # Normal position
            (0.0, 0.0),       # Zero position
            (250.5, 175.3),   # Decimal position
            (-10.0, -5.0),    # Negative position
        ]
        
        for i, test_pos in enumerate(test_positions):
            print(f"\n--- Testing Position {i+1}: {test_pos} ---")
            
            # Create mock stage
            mock_stage = Mock()
            mock_stage.get_position.return_value = test_pos
            mock_stage.set_zero = Mock()
            mock_stage.move_absolute = Mock()
            
            # Create ChipAlignmentSystem
            alignment_system = ChipAlignmentSystem(
                mock_stage,
                (100, 100, 800, 600),
                status_callback=lambda x: None
            )
            
            # Create mock edge detector
            edge_detector = BackgroundEdgeDetector(debug=False)
            edge_detector.is_on_chip = Mock(return_value=False)  # Find edge immediately
            
            try:
                # Test corner detection
                corner_coords = alignment_system._find_current_upper_left_corner(edge_detector)
                
                if corner_coords is not None:
                    print(f"✅ Position {test_pos} handled correctly: {corner_coords}")
                else:
                    print(f"⚠️ Position {test_pos} returned None (but no tuple errors)")
                
            except Exception as e:
                error_str = str(e)
                if "tuple indices must be integers or slices, not str" in error_str:
                    print(f"❌ Position {test_pos} has tuple indexing error: {error_str}")
                    return False
                else:
                    print(f"⚠️ Position {test_pos} had different error: {error_str}")
        
        print(f"\n✅ All position formats handled consistently!")
        return True
        
    except Exception as e:
        print(f"❌ Position format consistency test failed: {str(e)}")
        return False

def main():
    """Run all position tuple fix tests."""
    print("Testing Position Tuple Fix")
    print("=" * 60)
    
    tests = [
        test_position_tuple_handling,
        test_realignment_with_position_tuples,
        test_position_format_consistency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            print(f"✗ {test.__name__} CRASHED: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Position Tuple Fix Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 POSITION TUPLE FIX VERIFIED!")
        print("✅ Corner detection correctly handles (y, x) tuple format")
        print("✅ No 'tuple indices must be integers or slices, not str' errors")
        print("✅ Full re-alignment workflow works with position tuples")
        print("✅ Position format handling is consistent")
        return True
    else:
        print("\n❌ Some position tuple fix tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
