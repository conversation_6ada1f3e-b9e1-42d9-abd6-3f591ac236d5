#!/usr/bin/env python3
"""
Line Fitting Algorithm Demonstration GUI

This script provides a comprehensive GUI for testing and comparing the four line fitting
algorithm combinations available in the refactored edge detection architecture:
1. Sequential: Hough Transform → RANSAC refinement
2. Hough-Only: Hough Transform with line grouping/averaging
3. RANSAC-Only: Direct edge pixel → RANSAC fitting
4. Parallel: Both algorithms independently for comparison

Features:
- Live screen capture with region selection
- Image upload functionality
- Side-by-side visual comparison of all algorithm modes
- Performance metrics and confidence scores
- Real-time parameter adjustment
- Export functionality for results and images
"""

import sys
import time
import os
import cv2
import numpy as np
import mss
from datetime import datetime

# Fix KMeans memory leak warning on Windows with MKL
os.environ['OMP_NUM_THREADS'] = '1'
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QComboBox, QSlider, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QFileDialog, QMessageBox, QGroupBox,
                            QScrollArea, QSplitter, QTabWidget, QProgressBar,
                            QCheckBox, QFrame)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QPixmap, QImage, QFont

# Import the refactored edge detection classes
from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector, HoughLineFitter, RANSACLineFitter
from config import DEFAULT_REGION


class LineFittingWorker(QThread):
    """Worker thread for line fitting analysis to avoid blocking the GUI"""
    
    results_ready = pyqtSignal(dict)
    progress_update = pyqtSignal(int)
    
    def __init__(self, img, parameters, detector_type='CannyEdgeDetector'):
        super().__init__()
        self.img = img
        self.parameters = parameters
        self.detector_type = detector_type
        
    def run(self):
        """Run line fitting analysis with all algorithm modes"""
        try:
            results = {}

            # Initialize detector with parameters based on selection
            if self.detector_type == 'CannyEdgeDetector':
                detector = CannyEdgeDetector(debug=True)
            elif self.detector_type == 'BackgroundEdgeDetector':
                detector = BackgroundEdgeDetector(debug=True)
            elif self.detector_type == 'EdgeDetector':
                detector = EdgeDetector(debug=True)
            else:
                # Default to CannyEdgeDetector
                detector = CannyEdgeDetector(debug=True)

            print(f"Debug: Using {self.detector_type} for edge detection")

            self.progress_update.emit(10)

            # Validate input image
            if self.img is None:
                raise ValueError("Input image is None")

            if len(self.img.shape) not in [2, 3]:
                raise ValueError(f"Invalid image dimensions: {self.img.shape}")

            print(f"Debug: Processing image with shape: {self.img.shape}")

            # Test all four algorithm modes
            modes = ['sequential', 'hough_only', 'ransac_only', 'parallel']

            for i, mode in enumerate(modes):
                try:
                    print(f"Debug: Processing mode: {mode}")
                    start_time = time.time()

                    # Run edge detection with line fitting
                    result = detector.detect_edges_with_line_fitting(
                        self.img,
                        algorithm_mode=mode
                    )

                    end_time = time.time()
                    processing_time = end_time - start_time

                    # Validate result
                    if result is None:
                        result = {'error': f'No result returned for mode {mode}'}

                    # Add performance metrics
                    result['processing_time'] = processing_time
                    result['mode'] = mode

                    # Calculate additional metrics with error handling
                    try:
                        if 'edges' in result and result['edges'] is not None:
                            edge_pixels = np.sum(result['edges'] > 0)
                            result['edge_pixel_count'] = edge_pixels
                            print(f"Debug: Mode {mode} - {edge_pixels} edge pixels detected")
                        else:
                            result['edge_pixel_count'] = 0
                            print(f"Debug: Mode {mode} - no edges detected")
                    except Exception as metric_e:
                        print(f"Debug: Error calculating metrics for {mode}: {str(metric_e)}")
                        result['edge_pixel_count'] = 0

                    results[mode] = result

                except Exception as mode_e:
                    print(f"Debug: Error processing mode {mode}: {str(mode_e)}")
                    results[mode] = {
                        'error': str(mode_e),
                        'processing_time': 0,
                        'mode': mode,
                        'edge_pixel_count': 0
                    }

                # Update progress
                progress = 10 + (i + 1) * 20
                self.progress_update.emit(progress)

            # Add detector information to results
            results['detector_type'] = self.detector_type
            results['detector_info'] = f"Edge Detection: {self.detector_type}"

            self.progress_update.emit(100)
            self.results_ready.emit(results)

        except Exception as e:
            print(f"Debug: Critical error in LineFittingWorker: {str(e)}")
            error_result = {'error': str(e)}
            self.results_ready.emit(error_result)


class LineFittingDemoGUI(QMainWindow):
    """Main GUI for line fitting algorithm demonstration"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Line Fitting Algorithm Demonstration")
        self.setMinimumSize(1400, 900)
        
        # Current image and results
        self.current_image = None
        self.current_results = {}
        self.worker = None
        
        # Initialize UI
        self.init_ui()
        
        # Timer for live capture
        self.capture_timer = QTimer()
        self.capture_timer.timeout.connect(self.capture_live_image)
        
    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Main content area
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side: Image display and parameters
        left_widget = self.create_left_panel()
        content_splitter.addWidget(left_widget)
        
        # Right side: Results comparison
        right_widget = self.create_right_panel()
        content_splitter.addWidget(right_widget)
        
        # Set splitter proportions
        content_splitter.setSizes([500, 900])
        
        main_layout.addWidget(content_splitter)
        
        # Status bar
        self.statusBar().showMessage("Ready - Select image source and click 'Analyze'")
        
    def create_control_panel(self):
        """Create the control panel with image source and analysis controls"""
        group = QGroupBox("Image Source & Analysis")
        layout = QHBoxLayout()
        
        # Image source selection
        self.source_combo = QComboBox()
        self.source_combo.addItems(['Live Screen Capture', 'Upload Image File'])
        layout.addWidget(QLabel("Source:"))
        layout.addWidget(self.source_combo)

        # Edge detector selection
        self.detector_combo = QComboBox()
        self.detector_combo.addItems(['CannyEdgeDetector', 'BackgroundEdgeDetector', 'EdgeDetector'])
        self.detector_combo.setToolTip("Select edge detection algorithm:\n"
                                      "• CannyEdgeDetector: High-precision Canny edge detection\n"
                                      "• BackgroundEdgeDetector: Background-based detection\n"
                                      "• EdgeDetector: Basic edge detection")
        layout.addWidget(QLabel("Detector:"))
        layout.addWidget(self.detector_combo)

        # Live capture controls
        self.live_capture_btn = QPushButton("Start Live Capture")
        self.live_capture_btn.clicked.connect(self.toggle_live_capture)
        layout.addWidget(self.live_capture_btn)
        
        # Upload image button
        self.upload_btn = QPushButton("Upload Image")
        self.upload_btn.clicked.connect(self.upload_image)
        layout.addWidget(self.upload_btn)
        
        layout.addWidget(QFrame())  # Separator
        
        # Analysis controls
        self.analyze_btn = QPushButton("Analyze Line Fitting")
        self.analyze_btn.clicked.connect(self.analyze_line_fitting)
        self.analyze_btn.setStyleSheet("QPushButton { font-weight: bold; background-color: #4CAF50; color: white; }")
        layout.addWidget(self.analyze_btn)
        
        self.export_btn = QPushButton("Export Results")
        self.export_btn.clicked.connect(self.export_results)
        layout.addWidget(self.export_btn)
        
        layout.addStretch()
        
        group.setLayout(layout)
        return group
        
    def create_left_panel(self):
        """Create the left panel with image display and parameters"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Original image display
        image_group = QGroupBox("Original Image")
        image_layout = QVBoxLayout()
        
        self.image_label = QLabel("No image loaded")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(400, 300)
        self.image_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
        image_layout.addWidget(self.image_label)
        
        image_group.setLayout(image_layout)
        layout.addWidget(image_group)
        
        # Parameter controls
        params_group = self.create_parameter_controls()
        layout.addWidget(params_group)
        
        # Performance summary
        perf_group = self.create_performance_summary()
        layout.addWidget(perf_group)
        
        widget.setLayout(layout)
        return widget
        
    def create_parameter_controls(self):
        """Create parameter adjustment controls"""
        group = QGroupBox("Algorithm Parameters")
        layout = QGridLayout()
        
        # Canny parameters
        layout.addWidget(QLabel("Canny Low Threshold:"), 0, 0)
        self.canny_low_spin = QSpinBox()
        self.canny_low_spin.setRange(10, 200)
        self.canny_low_spin.setValue(50)
        layout.addWidget(self.canny_low_spin, 0, 1)
        
        layout.addWidget(QLabel("Canny High Threshold:"), 1, 0)
        self.canny_high_spin = QSpinBox()
        self.canny_high_spin.setRange(50, 300)
        self.canny_high_spin.setValue(150)
        layout.addWidget(self.canny_high_spin, 1, 1)
        
        # Hough parameters
        layout.addWidget(QLabel("Hough Min Line Length:"), 2, 0)
        self.hough_min_length_spin = QSpinBox()
        self.hough_min_length_spin.setRange(50, 500)
        self.hough_min_length_spin.setValue(100)
        layout.addWidget(self.hough_min_length_spin, 2, 1)
        
        layout.addWidget(QLabel("Hough Max Line Gap:"), 3, 0)
        self.hough_max_gap_spin = QSpinBox()
        self.hough_max_gap_spin.setRange(5, 50)
        self.hough_max_gap_spin.setValue(10)
        layout.addWidget(self.hough_max_gap_spin, 3, 1)
        
        # RANSAC parameters
        layout.addWidget(QLabel("RANSAC Distance Threshold:"), 4, 0)
        self.ransac_threshold_spin = QDoubleSpinBox()
        self.ransac_threshold_spin.setRange(1.0, 20.0)
        self.ransac_threshold_spin.setValue(5.0)
        self.ransac_threshold_spin.setSingleStep(0.5)
        layout.addWidget(self.ransac_threshold_spin, 4, 1)
        
        # Auto-update checkbox
        self.auto_update_cb = QCheckBox("Auto-update on parameter change")
        layout.addWidget(self.auto_update_cb, 5, 0, 1, 2)
        
        # Connect parameter changes to auto-update
        for widget in [self.canny_low_spin, self.canny_high_spin, self.hough_min_length_spin,
                      self.hough_max_gap_spin, self.ransac_threshold_spin]:
            widget.valueChanged.connect(self.on_parameter_changed)
        
        group.setLayout(layout)
        return group
        
    def create_performance_summary(self):
        """Create performance summary display"""
        group = QGroupBox("Performance Summary")
        layout = QVBoxLayout()
        
        self.performance_text = QTextEdit()
        self.performance_text.setMaximumHeight(150)
        self.performance_text.setReadOnly(True)
        layout.addWidget(self.performance_text)
        
        group.setLayout(layout)
        return group

    def create_right_panel(self):
        """Create the right panel with results comparison"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Results tabs
        self.results_tabs = QTabWidget()

        # Visual comparison tab
        visual_tab = self.create_visual_comparison_tab()
        self.results_tabs.addTab(visual_tab, "Visual Comparison")

        # Detailed results tab
        detailed_tab = self.create_detailed_results_tab()
        self.results_tabs.addTab(detailed_tab, "Detailed Results")

        layout.addWidget(self.results_tabs)

        widget.setLayout(layout)
        return widget

    def create_visual_comparison_tab(self):
        """Create visual comparison tab with side-by-side results"""
        widget = QWidget()
        layout = QGridLayout()

        # Create image displays for each algorithm mode
        self.result_labels = {}
        modes = [
            ('sequential', 'Sequential (Hough → RANSAC)'),
            ('hough_only', 'Hough Transform Only'),
            ('ransac_only', 'RANSAC Only'),
            ('parallel', 'Parallel Comparison')
        ]

        for i, (mode, title) in enumerate(modes):
            row = i // 2
            col = i % 2

            # Title
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("QLabel { font-weight: bold; }")
            layout.addWidget(title_label, row * 2, col)

            # Image display
            image_label = QLabel("No results")
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setMinimumSize(300, 200)
            image_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
            layout.addWidget(image_label, row * 2 + 1, col)

            self.result_labels[mode] = image_label

        widget.setLayout(layout)
        return widget

    def create_detailed_results_tab(self):
        """Create detailed results tab with metrics and data"""
        widget = QWidget()
        layout = QVBoxLayout()

        self.detailed_results_text = QTextEdit()
        self.detailed_results_text.setReadOnly(True)
        layout.addWidget(self.detailed_results_text)

        widget.setLayout(layout)
        return widget

    def toggle_live_capture(self):
        """Toggle live screen capture"""
        if self.capture_timer.isActive():
            self.capture_timer.stop()
            self.live_capture_btn.setText("Start Live Capture")
            self.statusBar().showMessage("Live capture stopped")
        else:
            self.capture_timer.start(1000)  # Update every second
            self.live_capture_btn.setText("Stop Live Capture")
            self.statusBar().showMessage("Live capture active")

    def capture_live_image(self):
        """Capture image from screen"""
        try:
            with mss.mss() as sct:
                shot = sct.grab(DEFAULT_REGION)
                img = np.array(shot)

            self.current_image = img
            self.display_image(img)

            # Auto-analyze if enabled
            if self.auto_update_cb.isChecked():
                self.analyze_line_fitting()

        except Exception as e:
            self.statusBar().showMessage(f"Capture error: {str(e)}")

    def upload_image(self):
        """Upload image from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 'Select Image File', '',
            'Image Files (*.png *.jpg *.jpeg *.bmp *.tiff);;All Files (*)'
        )

        if file_path:
            try:
                img = cv2.imread(file_path)
                if img is not None:
                    self.current_image = img
                    self.display_image(img)
                    self.statusBar().showMessage(f"Loaded: {file_path}")
                else:
                    QMessageBox.warning(self, "Error", "Failed to load image file")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading image: {str(e)}")

    def display_image(self, img):
        """Display image in the image label"""
        try:
            # Convert to RGB for Qt display
            if len(img.shape) == 3:
                rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_img.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            else:
                h, w = img.shape
                bytes_per_line = w
                qt_image = QImage(img.data, w, h, bytes_per_line, QImage.Format.Format_Grayscale8)

            pixmap = QPixmap.fromImage(qt_image)
            scaled_pixmap = pixmap.scaled(400, 300, Qt.AspectRatioMode.KeepAspectRatio)
            self.image_label.setPixmap(scaled_pixmap)

        except Exception as e:
            self.statusBar().showMessage(f"Display error: {str(e)}")

    def on_parameter_changed(self):
        """Handle parameter changes"""
        if self.auto_update_cb.isChecked() and self.current_image is not None:
            self.analyze_line_fitting()

    def get_current_parameters(self):
        """Get current parameter values"""
        return {
            'canny_low': self.canny_low_spin.value(),
            'canny_high': self.canny_high_spin.value(),
            'hough_min_length': self.hough_min_length_spin.value(),
            'hough_max_gap': self.hough_max_gap_spin.value(),
            'ransac_threshold': self.ransac_threshold_spin.value()
        }

    def analyze_line_fitting(self):
        """Analyze line fitting with all algorithm modes"""
        if self.current_image is None:
            QMessageBox.warning(self, "No Image", "Please capture or upload an image first")
            return

        if self.worker and self.worker.isRunning():
            return  # Analysis already in progress

        # Get current parameters
        parameters = self.get_current_parameters()

        # Get selected detector type
        detector_type = self.detector_combo.currentText()

        # Start analysis in worker thread
        self.worker = LineFittingWorker(self.current_image, parameters, detector_type)
        self.worker.results_ready.connect(self.on_results_ready)
        self.worker.progress_update.connect(self.progress_bar.setValue)

        # Show progress and disable controls
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.analyze_btn.setEnabled(False)
        self.statusBar().showMessage("Analyzing line fitting algorithms...")

        self.worker.start()

    def on_results_ready(self, results):
        """Handle analysis results"""
        self.current_results = results

        # Hide progress and re-enable controls
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)

        if 'error' in results:
            QMessageBox.critical(self, "Analysis Error", f"Analysis failed: {results['error']}")
            self.statusBar().showMessage("Analysis failed")
            return

        # Update displays
        self.update_visual_results(results)
        self.update_detailed_results(results)
        self.update_performance_summary(results)

        self.statusBar().showMessage("Analysis completed successfully")

    def update_visual_results(self, results):
        """Update visual comparison displays"""
        for mode, result in results.items():
            if mode in self.result_labels and isinstance(result, dict):
                try:
                    # Check if result has error
                    if 'error' in result:
                        self.result_labels[mode].setText(f"Error in {mode}:\n{result['error']}")
                        continue

                    # Create visualization for this mode
                    viz_img = self.create_mode_visualization(result)

                    # Convert to Qt pixmap and display
                    if viz_img is not None:
                        try:
                            # Ensure image is in correct format
                            if len(viz_img.shape) == 3:
                                rgb_img = cv2.cvtColor(viz_img, cv2.COLOR_BGR2RGB)
                                h, w, ch = rgb_img.shape
                                bytes_per_line = ch * w
                                qt_image = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
                            else:
                                # Grayscale image
                                h, w = viz_img.shape
                                bytes_per_line = w
                                qt_image = QImage(viz_img.data, w, h, bytes_per_line, QImage.Format.Format_Grayscale8)

                            pixmap = QPixmap.fromImage(qt_image)
                            scaled_pixmap = pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio)
                            self.result_labels[mode].setPixmap(scaled_pixmap)

                        except Exception as display_e:
                            print(f"Debug: Display error for {mode}: {str(display_e)}")
                            self.result_labels[mode].setText(f"Display error:\n{str(display_e)}")
                    else:
                        self.result_labels[mode].setText("Visualization failed\n(see console for details)")

                except Exception as e:
                    print(f"Debug: Visualization error for {mode}: {str(e)}")
                    self.result_labels[mode].setText(f"Error: {str(e)}")

    def create_mode_visualization(self, result):
        """Create visualization for a specific algorithm mode"""
        try:
            # Start with original image
            viz_img = self.current_image.copy()

            # Draw edges if available
            if 'edges' in result and result['edges'] is not None:
                edges = result['edges']

                # Validate and fix dimensions before blending
                viz_img = self._blend_edges_with_image(viz_img, edges)
                if viz_img is None:
                    # Fallback: use original image if blending fails
                    viz_img = self.current_image.copy()

            # Draw detected lines if available
            if 'lines' in result and result['lines']:
                for line in result['lines']:
                    if len(line) >= 4:
                        x1, y1, x2, y2 = line[:4]
                        cv2.line(viz_img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

            # Draw fitted lines if available
            fitted_lines = result.get('fitted_lines', {})

            # Draw horizontal fitted line (blue)
            if fitted_lines.get('horizontal'):
                self._draw_fitted_line(viz_img, fitted_lines['horizontal'], 'horizontal', (255, 0, 0))

            # Draw vertical fitted line (red)
            if fitted_lines.get('vertical'):
                self._draw_fitted_line(viz_img, fitted_lines['vertical'], 'vertical', (0, 0, 255))

            return viz_img

        except Exception as e:
            print(f"Visualization error: {str(e)}")
            return None

    def _draw_fitted_line(self, img, line_data, orientation, color):
        """Draw a fitted line on the image"""
        try:
            h, w = img.shape[:2]

            if isinstance(line_data, dict) and 'slope' in line_data:
                slope = line_data['slope']
                intercept = line_data['intercept']

                if orientation == 'horizontal':
                    # y = mx + b
                    y1 = int(slope * 0 + intercept)
                    y2 = int(slope * (w-1) + intercept)
                    cv2.line(img, (0, y1), (w-1, y2), color, 3)
                else:
                    # x = my + b (for vertical lines)
                    x1 = int(slope * 0 + intercept)
                    x2 = int(slope * (h-1) + intercept)
                    cv2.line(img, (x1, 0), (x2, h-1), color, 3)

            elif isinstance(line_data, (list, tuple)) and len(line_data) >= 4:
                # Direct line coordinates
                x1, y1, x2, y2 = line_data[:4]
                cv2.line(img, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

        except Exception as e:
            print(f"Line drawing error: {str(e)}")

    def _blend_edges_with_image(self, img, edges):
        """
        Safely blend edge detection results with original image.
        Handles dimension mismatches and provides fallback options.

        Args:
            img: Original image (BGR format)
            edges: Edge detection result (grayscale)

        Returns:
            Blended image or None if blending fails
        """
        try:
            # Debug logging for dimension analysis
            print(f"Debug: Original image shape: {img.shape}")
            print(f"Debug: Edges shape: {edges.shape}")

            # Ensure edges is 2D grayscale
            if len(edges.shape) == 3:
                edges = cv2.cvtColor(edges, cv2.COLOR_BGR2GRAY)

            # Ensure original image is 3-channel BGR
            if len(img.shape) == 2:
                # Convert grayscale to BGR
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                print(f"Debug: Converted grayscale image to BGR: {img.shape}")
            elif len(img.shape) == 3 and img.shape[2] == 4:
                # Convert BGRA to BGR
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                print(f"Debug: Converted BGRA image to BGR: {img.shape}")

            # Get dimensions
            img_h, img_w = img.shape[:2]
            edge_h, edge_w = edges.shape[:2]

            # Check if dimensions match
            if img_h != edge_h or img_w != edge_w:
                print(f"Debug: Dimension mismatch - resizing edges from {edges.shape} to {(img_h, img_w)}")
                # Resize edges to match image dimensions
                edges = cv2.resize(edges, (img_w, img_h), interpolation=cv2.INTER_NEAREST)

            # Convert edges to 3-channel BGR for blending
            edge_overlay = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

            # Ensure both images have the same data type
            if img.dtype != edge_overlay.dtype:
                edge_overlay = edge_overlay.astype(img.dtype)

            # Create blue edge overlay (keep only blue channel for edges)
            edge_overlay[:, :, 1] = 0  # Remove green channel
            edge_overlay[:, :, 2] = 0  # Remove red channel, keep blue for edges

            # Final dimension check before blending
            if img.shape != edge_overlay.shape:
                print(f"Debug: Shape mismatch after conversion - img: {img.shape}, overlay: {edge_overlay.shape}")
                return None

            # Blend with original image
            blended = cv2.addWeighted(img, 0.7, edge_overlay, 0.3, 0)
            print(f"Debug: Blending successful, result shape: {blended.shape}")

            return blended

        except Exception as e:
            print(f"Debug: Edge blending failed: {str(e)}")
            print(f"Debug: Exception type: {type(e).__name__}")

            # Try alternative blending approach
            try:
                return self._fallback_edge_visualization(img, edges)
            except Exception as fallback_e:
                print(f"Debug: Fallback visualization also failed: {str(fallback_e)}")
                return None

    def _fallback_edge_visualization(self, img, edges):
        """
        Fallback edge visualization that draws edges as contours instead of blending.

        Args:
            img: Original image
            edges: Edge detection result

        Returns:
            Image with edge contours drawn
        """
        try:
            viz_img = img.copy()

            # Ensure edges is 2D grayscale
            if len(edges.shape) == 3:
                edges = cv2.cvtColor(edges, cv2.COLOR_BGR2GRAY)

            # Resize edges if needed
            img_h, img_w = img.shape[:2]
            if edges.shape != (img_h, img_w):
                edges = cv2.resize(edges, (img_w, img_h), interpolation=cv2.INTER_NEAREST)

            # Find contours and draw them
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(viz_img, contours, -1, (255, 0, 0), 2)  # Blue contours

            print(f"Debug: Fallback visualization successful with {len(contours)} contours")
            return viz_img

        except Exception as e:
            print(f"Debug: Fallback visualization failed: {str(e)}")
            raise

    def update_detailed_results(self, results):
        """Update detailed results display"""
        try:
            text = "=== LINE FITTING ALGORITHM COMPARISON ===\n\n"

            for mode, result in results.items():
                # Skip non-dictionary items (like 'detector_type', 'detector_info')
                if not isinstance(result, dict):
                    continue

                text += f"--- {mode.upper().replace('_', ' ')} MODE ---\n"
                text += f"Processing Time: {result.get('processing_time', 0):.3f} seconds\n"
                text += f"Edge Pixels: {result.get('edge_pixel_count', 0)}\n"

                # Line detection results
                lines = result.get('lines', [])
                text += f"Detected Lines: {len(lines) if lines else 0}\n"

                # Fitted lines results
                fitted_lines = result.get('fitted_lines', {})
                if fitted_lines:
                    h_line = fitted_lines.get('horizontal')
                    v_line = fitted_lines.get('vertical')

                    text += f"Horizontal Line: {'✓' if h_line else '✗'}\n"
                    if h_line and isinstance(h_line, dict):
                        confidence = h_line.get('confidence', 'N/A')
                        text += f"  Confidence: {confidence}\n"

                    text += f"Vertical Line: {'✓' if v_line else '✗'}\n"
                    if v_line and isinstance(v_line, dict):
                        confidence = v_line.get('confidence', 'N/A')
                        text += f"  Confidence: {confidence}\n"

                text += "\n"

            self.detailed_results_text.setText(text)

        except Exception as e:
            self.detailed_results_text.setText(f"Error updating detailed results: {str(e)}")

    def update_performance_summary(self, results):
        """Update performance summary display"""
        try:
            text = "PERFORMANCE SUMMARY\n"
            text += "=" * 30 + "\n\n"

            # Detector information
            detector_type = results.get('detector_type', 'Unknown')
            text += f"Edge Detector: {detector_type}\n\n"

            # Processing times comparison
            times = {}
            for mode, result in results.items():
                # Skip non-dictionary items (like 'detector_type', 'detector_info')
                if isinstance(result, dict):
                    times[mode] = result.get('processing_time', 0)

            if times:  # Only proceed if we have timing data
                fastest_mode = min(times, key=times.get)
                slowest_mode = max(times, key=times.get)

                text += f"Fastest: {fastest_mode} ({times[fastest_mode]:.3f}s)\n"
                text += f"Slowest: {slowest_mode} ({times[slowest_mode]:.3f}s)\n\n"
            else:
                text += "No timing data available\n\n"

            # Success rates
            success_count = 0
            total_modes = 0
            for mode, result in results.items():
                # Skip non-dictionary items (like 'detector_type', 'detector_info')
                if isinstance(result, dict):
                    total_modes += 1
                    fitted_lines = result.get('fitted_lines', {})
                    if fitted_lines and fitted_lines.get('horizontal') and fitted_lines.get('vertical'):
                        success_count += 1

            text += f"Success Rate: {success_count}/{total_modes} modes\n\n"

            # Recommendations
            text += "RECOMMENDATIONS:\n"
            if success_count == total_modes and total_modes > 0:
                text += f"• Use {fastest_mode} for speed\n"
                text += "• Use sequential for accuracy\n"
            elif success_count > 0:
                text += "• Adjust parameters for better results\n"
                text += "• Consider different algorithm modes\n"
            else:
                text += "• Check image quality\n"
                text += "• Adjust Canny thresholds\n"
                text += "• Verify edge detection parameters\n"

            self.performance_text.setText(text)

        except Exception as e:
            self.performance_text.setText(f"Error updating performance summary: {str(e)}")

    def export_results(self):
        """Export analysis results and images"""
        if not self.current_results:
            QMessageBox.warning(self, "No Results", "Please run analysis first")
            return

        try:
            # Get export directory
            export_dir = QFileDialog.getExistingDirectory(self, "Select Export Directory")
            if not export_dir:
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Export original image
            if self.current_image is not None:
                original_path = f"{export_dir}/original_{timestamp}.png"
                cv2.imwrite(original_path, self.current_image)

            # Export visualization images for each mode
            for mode, result in self.current_results.items():
                viz_img = self.create_mode_visualization(result)
                if viz_img is not None:
                    viz_path = f"{export_dir}/{mode}_{timestamp}.png"
                    cv2.imwrite(viz_path, viz_img)

            # Export detailed results as text
            results_path = f"{export_dir}/analysis_results_{timestamp}.txt"
            with open(results_path, 'w') as f:
                f.write(self.detailed_results_text.toPlainText())
                f.write("\n\n")
                f.write(self.performance_text.toPlainText())

            QMessageBox.information(self, "Export Complete",
                                  f"Results exported to:\n{export_dir}")

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export results: {str(e)}")


def main():
    """Main function to run the demonstration GUI"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Line Fitting Algorithm Demo")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = LineFittingDemoGUI()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
