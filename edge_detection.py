"""
Edge Detection Module for Silicon Chip Scanning

This module contains classes for detecting the edges of silicon chips in images.
Includes rotation-robust algorithms for handling chips rotated >20 degrees.
"""

import cv2
import numpy as np
import time
import os
from typing import Tuple, Optional, List, Dict
from sklearn.linear_model import RANSACRegressor
from config import EDGE_DETECTION_PARAMS

# Fix KMeans memory leak warning on Windows with MKL
os.environ['OMP_NUM_THREADS'] = '1'


class HoughLineFitter:
    """
    Independent line fitting class using Hough Transform.
    Supports line detection, grouping, and averaging for similar lines.
    """

    def __init__(self, debug=False):
        self.debug = debug

    def fit_lines(self, edges, min_line_length=100, max_line_gap=10, threshold=50):
        """
        Detect lines using Probabilistic Hough Transform.

        Args:
            edges: Binary edge image from Canny detector
            min_line_length: Minimum length of line segments
            max_line_gap: Maximum gap between line segments
            threshold: Minimum number of votes for line detection

        Returns:
            lines: List of detected lines [(x1, y1, x2, y2), ...]
        """
        # Apply Probabilistic Hough Transform
        lines = cv2.HoughLinesP(
            edges,
            rho=1,              # Distance resolution in pixels
            theta=np.pi/180,    # Angular resolution in radians
            threshold=threshold,
            minLineLength=min_line_length,
            maxLineGap=max_line_gap
        )

        if lines is not None:
            return lines.reshape(-1, 4).tolist()
        return []

    def group_similar_lines(self, lines, angle_threshold=10, distance_threshold=20):
        """
        Group similar lines based on angle and distance.

        Args:
            lines: List of line segments [(x1, y1, x2, y2), ...]
            angle_threshold: Maximum angle difference in degrees for grouping
            distance_threshold: Maximum distance for parallel lines

        Returns:
            Dict with grouped lines: {'horizontal': [...], 'vertical': [...]}
        """
        if not lines:
            return {'horizontal': [], 'vertical': []}

        horizontal_lines = []
        vertical_lines = []

        for line in lines:
            x1, y1, x2, y2 = line
            angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi

            # Classify as horizontal or vertical (with tolerance)
            if abs(angle) < angle_threshold or abs(angle) > (180 - angle_threshold):
                horizontal_lines.append(line)
            elif (90 - angle_threshold) < abs(angle) < (90 + angle_threshold):
                vertical_lines.append(line)

        return {
            'horizontal': self._group_parallel_lines(horizontal_lines, distance_threshold),
            'vertical': self._group_parallel_lines(vertical_lines, distance_threshold)
        }

    def _group_parallel_lines(self, lines, distance_threshold):
        """
        Group parallel lines that are close to each other.

        Args:
            lines: List of parallel line segments
            distance_threshold: Maximum distance between parallel lines

        Returns:
            List of line groups, each group is a list of similar lines
        """
        if not lines:
            return []

        groups = []
        used = [False] * len(lines)

        for i, line1 in enumerate(lines):
            if used[i]:
                continue

            group = [line1]
            used[i] = True

            for j, line2 in enumerate(lines[i+1:], i+1):
                if used[j]:
                    continue

                # Calculate distance between parallel lines
                distance = self._calculate_line_distance(line1, line2)
                if distance < distance_threshold:
                    group.append(line2)
                    used[j] = True

            groups.append(group)

        return groups

    def _calculate_line_distance(self, line1, line2):
        """
        Calculate the distance between two parallel lines.

        Args:
            line1, line2: Line segments [(x1, y1, x2, y2)]

        Returns:
            distance: Perpendicular distance between lines
        """
        x1, y1, x2, y2 = line1
        x3, y3, x4, y4 = line2

        # Calculate line parameters for line1: ax + by + c = 0
        a1 = y2 - y1
        b1 = x1 - x2
        c1 = x2 * y1 - x1 * y2

        # Normalize
        norm1 = np.sqrt(a1**2 + b1**2)
        if norm1 == 0:
            return float('inf')
        a1, b1, c1 = a1/norm1, b1/norm1, c1/norm1

        # Calculate distance from midpoint of line2 to line1
        mid_x = (x3 + x4) / 2
        mid_y = (y3 + y4) / 2
        distance = abs(a1 * mid_x + b1 * mid_y + c1)

        return distance

    def average_line_groups(self, line_groups):
        """
        Average lines within each group to get representative lines.

        Args:
            line_groups: List of line groups from group_similar_lines

        Returns:
            List of averaged lines [(x1, y1, x2, y2), ...]
        """
        averaged_lines = []

        for group in line_groups:
            if not group:
                continue

            # Calculate average line parameters
            avg_line = self._average_lines(group)
            if avg_line is not None:
                averaged_lines.append(avg_line)

        return averaged_lines

    def _average_lines(self, lines):
        """
        Calculate the average line from a group of similar lines.

        Args:
            lines: List of similar line segments

        Returns:
            Averaged line (x1, y1, x2, y2) or None if invalid
        """
        if not lines:
            return None

        # Extract all points
        points = []
        for line in lines:
            x1, y1, x2, y2 = line
            points.extend([(x1, y1), (x2, y2)])

        points = np.array(points)

        # Fit line using least squares
        try:
            # Calculate centroid
            centroid = np.mean(points, axis=0)

            # Calculate covariance matrix
            centered_points = points - centroid
            cov_matrix = np.cov(centered_points.T)

            # Get principal component (direction of line)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
            principal_component = eigenvectors[:, np.argmax(eigenvalues)]

            # Calculate line extent
            projections = np.dot(centered_points, principal_component)
            min_proj, max_proj = np.min(projections), np.max(projections)

            # Calculate line endpoints
            start_point = centroid + min_proj * principal_component
            end_point = centroid + max_proj * principal_component

            return [int(start_point[0]), int(start_point[1]),
                   int(end_point[0]), int(end_point[1])]

        except Exception as e:
            if self.debug:
                print(f"Error averaging lines: {str(e)}")
            return None


class RANSACLineFitter:
    """
    Independent line fitting class using RANSAC algorithm.
    Supports robust line fitting with outlier detection and validation.
    """

    def __init__(self, debug=False):
        self.debug = debug

    def edges_to_points(self, edges):
        """
        Convert binary edge image to coordinate points.

        Args:
            edges: Binary edge image (0 or 255 values)

        Returns:
            points: Array of (x, y) coordinates where edges are detected
        """
        # Find all edge pixels
        edge_pixels = np.where(edges > 0)

        # Convert to (x, y) coordinates
        points = np.column_stack((edge_pixels[1], edge_pixels[0]))  # (x, y) format

        return points

    def fit_lines(self, lines, distance_threshold=5.0):
        """
        Robust line fitting using RANSAC algorithm.

        Args:
            lines: List of line segments from Hough transform or edge points
            distance_threshold: Maximum distance for inliers

        Returns:
            Dict containing fitted lines for horizontal and vertical edges
        """
        if lines is None or (hasattr(lines, '__len__') and len(lines) == 0):
            return {'horizontal': None, 'vertical': None}

        # If lines is a list of line segments, convert to points
        if isinstance(lines[0], (list, tuple)) and len(lines[0]) == 4:
            points = self._lines_to_points(lines)
        else:
            points = lines

        # Separate points into horizontal and vertical based on local gradients
        horizontal_points, vertical_points = self._separate_points_by_orientation(points)

        result = {'horizontal': None, 'vertical': None}

        # Fit horizontal lines using RANSAC
        if len(horizontal_points) >= 2:
            result['horizontal'] = self._fit_line_ransac(horizontal_points, 'horizontal', distance_threshold)

        # Fit vertical lines using RANSAC
        if len(vertical_points) >= 2:
            result['vertical'] = self._fit_line_ransac(vertical_points, 'vertical', distance_threshold)

        return result

    def _lines_to_points(self, lines):
        """
        Convert line segments to individual points.

        Args:
            lines: List of line segments [(x1, y1, x2, y2), ...]

        Returns:
            points: Array of (x, y) coordinates
        """
        points = []
        for line in lines:
            x1, y1, x2, y2 = line
            points.extend([(x1, y1), (x2, y2)])

        return np.array(points)

    def _separate_points_by_orientation(self, points):
        """
        Separate points into horizontal and vertical groups based on local gradients.

        Args:
            points: Array of (x, y) coordinates

        Returns:
            Tuple of (horizontal_points, vertical_points)
        """
        if len(points) < 4:
            return points, np.array([])

        # For edge points, we can use clustering or simple angle-based separation
        # Here we use a simple approach based on the distribution of points

        # Calculate the principal components to determine dominant orientations
        try:
            centroid = np.mean(points, axis=0)
            centered_points = points - centroid
            cov_matrix = np.cov(centered_points.T)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

            # Get the principal direction
            principal_direction = eigenvectors[:, np.argmax(eigenvalues)]
            angle = np.arctan2(principal_direction[1], principal_direction[0]) * 180 / np.pi

            # If principal direction is more horizontal, separate based on y-coordinate variance
            # If more vertical, separate based on x-coordinate variance
            if abs(angle) < 45 or abs(angle) > 135:
                # Principal direction is horizontal, separate by y-coordinate clusters
                return self._separate_by_coordinate(points, axis=1)  # y-axis
            else:
                # Principal direction is vertical, separate by x-coordinate clusters
                return self._separate_by_coordinate(points, axis=0)  # x-axis

        except Exception as e:
            if self.debug:
                print(f"Error in point separation: {str(e)}")
            # Fallback: split points randomly
            mid = len(points) // 2
            return points[:mid], points[mid:]

    def _separate_by_coordinate(self, points, axis):
        """
        Separate points into two groups based on coordinate clustering.

        Args:
            points: Array of (x, y) coordinates
            axis: 0 for x-coordinate, 1 for y-coordinate

        Returns:
            Tuple of two point groups
        """
        coordinates = points[:, axis]

        # Use simple k-means clustering (k=2) on the specified coordinate
        try:
            from sklearn.cluster import KMeans
            # Fix memory leak warning by setting n_init explicitly and using 'lloyd' algorithm
            kmeans = KMeans(n_clusters=2, random_state=42, n_init=10, algorithm='lloyd')
            labels = kmeans.fit_predict(coordinates.reshape(-1, 1))

            group1 = points[labels == 0]
            group2 = points[labels == 1]

            return group1, group2

        except (ImportError, Exception) as e:
            # Fallback: simple median-based separation (more robust and no memory issues)
            if self.debug:
                print(f"KMeans clustering failed, using median-based separation: {str(e)}")
            median = np.median(coordinates)
            mask = coordinates < median
            return points[mask], points[~mask]

    def _fit_line_ransac(self, points, orientation, distance_threshold):
        """
        Fit a single line using RANSAC for either horizontal or vertical orientation.

        Args:
            points: Array of (x, y) coordinates
            orientation: 'horizontal' or 'vertical'
            distance_threshold: Maximum distance for inliers

        Returns:
            Dict with line parameters and inlier information
        """
        if len(points) < 2:
            return None

        try:
            if orientation == 'horizontal':
                # For horizontal lines, fit y = mx + b (x as input, y as output)
                X = points[:, 0].reshape(-1, 1)  # x coordinates
                y = points[:, 1]                 # y coordinates
            else:
                # For vertical lines, fit x = my + b (y as input, x as output)
                X = points[:, 1].reshape(-1, 1)  # y coordinates
                y = points[:, 0]                 # x coordinates

            # Apply RANSAC
            ransac = RANSACRegressor(
                residual_threshold=distance_threshold,
                max_trials=100,
                random_state=42
            )
            ransac.fit(X, y)

            # Get line parameters
            slope = ransac.estimator_.coef_[0]
            intercept = ransac.estimator_.intercept_

            # Calculate inlier mask and score
            inlier_mask = ransac.inlier_mask_
            inlier_count = np.sum(inlier_mask)
            total_points = len(points)
            confidence = inlier_count / total_points

            return {
                'slope': slope,
                'intercept': intercept,
                'orientation': orientation,
                'inlier_count': inlier_count,
                'total_points': total_points,
                'confidence': confidence,
                'inlier_mask': inlier_mask,
                'inlier_points': points[inlier_mask]
            }

        except Exception as e:
            if self.debug:
                print(f"RANSAC fitting failed for {orientation} lines: {str(e)}")
            return None

    def validate_line_fit(self, line_result, min_inliers=10, min_confidence=0.3):
        """
        Validate the quality of a line fit result.

        Args:
            line_result: Result dict from _fit_line_ransac
            min_inliers: Minimum number of inlier points required
            min_confidence: Minimum confidence score required

        Returns:
            bool: True if line fit is valid, False otherwise
        """
        if line_result is None:
            return False

        return (line_result['inlier_count'] >= min_inliers and
                line_result['confidence'] >= min_confidence)

    def line_to_endpoints(self, line_result, img_shape):
        """
        Convert line parameters to endpoint coordinates for visualization.

        Args:
            line_result: Result dict from _fit_line_ransac
            img_shape: (height, width) of the image

        Returns:
            Tuple of ((x1, y1), (x2, y2)) endpoints
        """
        if line_result is None:
            return None

        h, w = img_shape
        slope = line_result['slope']
        intercept = line_result['intercept']
        orientation = line_result['orientation']

        if orientation == 'horizontal':
            # y = mx + b, calculate endpoints at image boundaries
            x1, x2 = 0, w - 1
            y1 = slope * x1 + intercept
            y2 = slope * x2 + intercept
        else:
            # x = my + b, calculate endpoints at image boundaries
            y1, y2 = 0, h - 1
            x1 = slope * y1 + intercept
            x2 = slope * y2 + intercept

        # Clamp to image boundaries
        x1 = max(0, min(w - 1, x1))
        x2 = max(0, min(w - 1, x2))
        y1 = max(0, min(h - 1, y1))
        y2 = max(0, min(h - 1, y2))

        return ((int(x1), int(y1)), (int(x2), int(y2)))


class EdgeDetector:
    """
    Detects edges of silicon chips based on color/intensity differences.
    Enhanced with rotation-robust algorithms.
    """

    def __init__(self, threshold_low=None, threshold_high=None, debug=False):
        self.threshold_low = threshold_low or EDGE_DETECTION_PARAMS['threshold_low']
        self.threshold_high = threshold_high or EDGE_DETECTION_PARAMS['threshold_high']
        self.debug = debug
        # Initialize line fitters for standardized interface
        self.hough_fitter = HoughLineFitter(debug=debug)
        self.ransac_fitter = RANSACLineFitter(debug=debug)

    def detect_chip_edges(self, img):
        """
        Detect silicon chip edges in the image with rotation robustness.
        Returns: mask where True = chip area, False = outside chip
        """
        # Convert to grayscale
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Enhanced rotation-robust edge detection
        return self._detect_edges_rotation_robust(img, gray)

    def _detect_edges_rotation_robust(self, img, gray):
        """Enhanced edge detection that handles rotated chips"""
        h, w = gray.shape

        # Method 1: Multi-scale edge detection for rotation robustness
        edges_combined = np.zeros_like(gray)

        # Apply edge detection at multiple scales and orientations
        for scale in [0.5, 1.0, 1.5]:
            kernel_size = max(3, int(5 * scale))
            if kernel_size % 2 == 0:
                kernel_size += 1

            blurred = cv2.GaussianBlur(gray, (kernel_size, kernel_size), scale)
            edges = cv2.Canny(blurred,
                            int(self.threshold_low * scale),
                            int(self.threshold_high * scale))
            edges_combined = cv2.bitwise_or(edges_combined, edges)

        # Method 2: Rotation-invariant color-based detection
        chip_mask_color = self._detect_chip_by_color_robust(img)

        # Method 3: Morphological operations with rotation-invariant kernels
        # Use circular kernels instead of rectangular ones for rotation invariance
        kernel_circle = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
        edges_dilated = cv2.dilate(edges_combined, kernel_circle, iterations=2)

        # Combine color and edge information
        combined_mask = cv2.bitwise_or(chip_mask_color, edges_dilated)

        # Fill holes and clean up with rotation-invariant operations
        chip_mask_filled = self._fill_and_clean_mask(combined_mask)

        # Find the largest connected component (the chip)
        final_mask = self._extract_largest_component(chip_mask_filled, h, w)

        # Debug visualization
        if self.debug:
            self._save_debug_images(img, edges_combined, chip_mask_color, final_mask)

        return final_mask

    def _detect_chip_by_color_robust(self, img):
        """Rotation-invariant color-based chip detection"""
        if len(img.shape) != 3:
            # If grayscale, use intensity threshold
            gray = img if len(img.shape) == 2 else cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            # For grayscale, assume darker regions are chip (silicon is typically darker)
            _, chip_mask_color = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            return chip_mask_color

        # Enhanced HSV-based detection with multiple color ranges
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # CRITICAL FIX: Correct HSV ranges based on actual beige color analysis
        # Beige background has H=105, S=57, V=180, so we need ranges that include this
        background_ranges = [
            ([15, 30, 150], [35, 120, 255]),   # Yellow/orange beige
            ([90, 30, 140], [120, 120, 255]),  # FIXED: Cyan/green beige (H=105)
            ([0, 0, 200], [180, 40, 255]),     # Very light/white background (low saturation)
            ([0, 10, 160], [180, 80, 255]),    # Light colored backgrounds (broader range)
        ]

        background_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        for lower, upper in background_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            background_mask = cv2.bitwise_or(background_mask, mask)

        # Clean up background detection with morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_OPEN, kernel)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_CLOSE, kernel)

        # The chip is everything that's NOT background
        chip_mask_color = cv2.bitwise_not(background_mask)

        # Additional validation: if background covers >90% of image, likely all background
        h, w = hsv.shape[:2]
        background_ratio = np.sum(background_mask > 0) / (h * w)
        if background_ratio > 0.90:
            # Image is mostly background, return minimal chip mask
            chip_mask_color = np.zeros((h, w), np.uint8)

        return chip_mask_color

    def _fill_and_clean_mask(self, mask):
        """Fill holes and clean mask using rotation-invariant operations"""
        # Use circular kernels for rotation invariance
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (30, 30))

        # CRITICAL FIX: Check if mask is mostly empty before processing
        h, w = mask.shape
        mask_ratio = np.sum(mask > 0) / (h * w)

        if mask_ratio < 0.05:  # If less than 5% of image is detected as chip
            # Return empty mask - don't try to fill holes in mostly empty mask
            return np.zeros((h, w), np.uint8)

        # Clean up with morphological operations (skip problematic flood fill)
        mask_cleaned = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_large)
        mask_cleaned = cv2.morphologyEx(mask_cleaned, cv2.MORPH_OPEN, kernel_small)

        return mask_cleaned

    def _extract_largest_component(self, mask, h, w):
        """Extract the largest connected component"""
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(mask)
        if num_labels > 1:
            # Get the largest component (excluding background)
            areas = stats[1:, cv2.CC_STAT_AREA]
            largest_idx = 1 + np.argmax(areas)

            # Only accept if it's significantly large (at least 15% of image)
            if areas[largest_idx - 1] > 0.15 * h * w:
                final_mask = (labels == largest_idx).astype(np.uint8) * 255
            else:
                # CRITICAL FIX: If no large component, return EMPTY mask (no chip detected)
                # This handles the case where image is mostly/entirely background
                final_mask = np.zeros((h, w), np.uint8)
        else:
            # If no components found, check if original mask has significant content
            chip_ratio = np.sum(mask > 0) / (h * w)
            if chip_ratio > 0.15:  # At least 15% of image should be chip
                final_mask = mask
            else:
                # CRITICAL FIX: Return empty mask for mostly background images
                final_mask = np.zeros((h, w), np.uint8)

        return final_mask

    def _save_debug_images(self, img, edges, color_mask, final_mask):
        """Save debug images for analysis"""
        timestamp = int(time.time())
        debug_img = img.copy()

        # Draw detected edges in green
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)

        # Save intermediate steps
        cv2.imwrite(f"edge_debug_edges_{timestamp}.png", edges)
        cv2.imwrite(f"edge_debug_color_{timestamp}.png", color_mask)
        cv2.imwrite(f"edge_debug_final_{timestamp}.png", debug_img)
        cv2.imwrite(f"edge_debug_mask_{timestamp}.png", final_mask)

    def find_chip_boundaries(self, mask):
        """Find the boundaries of the chip from the mask"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None

        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)

        return x, x + w, y, y + h

    def find_rotated_chip_boundaries(self, mask):
        """Find boundaries of rotated chip using minimum area rectangle"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None

        largest_contour = max(contours, key=cv2.contourArea)

        # Get minimum area rectangle (handles rotation)
        rect = cv2.minAreaRect(largest_contour)
        box = cv2.boxPoints(rect)
        box = np.int0(box)

        # Return center, size, and angle
        center, (width, height), angle = rect
        return {
            'center': center,
            'size': (width, height),
            'angle': angle,
            'corners': box
        }

    def is_on_chip(self, img, x, y, margin=10):
        """
        Check if a specific pixel position is on the chip with rotation robustness
        margin: pixels from edge to consider as boundary
        """
        mask = self.detect_chip_edges(img)
        h, w = mask.shape

        # Check bounds
        if x < 0 or x >= w or y < 0 or y >= h:
            return False

        # For rotation robustness, check a circular region instead of rectangular
        # Create circular mask around the point
        center = (x, y)
        radius = margin

        # Create a circular region mask
        y_indices, x_indices = np.ogrid[:h, :w]
        circular_mask = (x_indices - center[0])**2 + (y_indices - center[1])**2 <= radius**2

        # Apply bounds
        circular_mask = circular_mask & (x_indices >= 0) & (x_indices < w) & (y_indices >= 0) & (y_indices < h)

        # Check if the majority of pixels in the circular area are on-chip
        region_pixels = mask[circular_mask]
        if len(region_pixels) == 0:
            return False

        on_chip_pixels = np.sum(region_pixels > 0)
        total_pixels = len(region_pixels)

        # Require at least 70% of pixels to be on-chip (more lenient for rotation)
        return on_chip_pixels > 0.7 * total_pixels

    def fit_lines_hough(self, edges, min_line_length=100, max_line_gap=10, threshold=50):
        """
        Detect lines using Probabilistic Hough Transform.
        Uses the independent HoughLineFitter class.

        Args:
            edges: Binary edge image from edge detector
            min_line_length: Minimum length of line segments
            max_line_gap: Maximum gap between line segments
            threshold: Minimum number of votes for line detection

        Returns:
            lines: List of detected lines [(x1, y1, x2, y2), ...]
        """
        if not hasattr(self, 'hough_fitter'):
            self.hough_fitter = HoughLineFitter(debug=getattr(self, 'debug', False))
        return self.hough_fitter.fit_lines(edges, min_line_length, max_line_gap, threshold)

    def fit_lines_ransac(self, lines, distance_threshold=5.0):
        """
        Robust line fitting using RANSAC algorithm.
        Uses the independent RANSACLineFitter class.

        Args:
            lines: List of line segments from Hough transform
            distance_threshold: Maximum distance for inliers

        Returns:
            Dict containing fitted lines for horizontal and vertical edges
        """
        if not hasattr(self, 'ransac_fitter'):
            self.ransac_fitter = RANSACLineFitter(debug=getattr(self, 'debug', False))
        return self.ransac_fitter.fit_lines(lines, distance_threshold)

    def detect_edges_with_line_fitting(self, img, algorithm_mode='ransac_only'):
        """
        Comprehensive edge detection using edge detection + line fitting algorithms.
        Supports flexible algorithm combinations as specified in requirements.

        Args:
            img: Input image
            algorithm_mode: 'sequential', 'hough_only', 'ransac_only', or 'parallel'

        Returns:
            Dict with edge detection results and fitted lines
        """
        # Step 1: Edge detection using this class's own method
        # Use this class's detect_chip_edges() method to get chip mask
        chip_mask = self.detect_chip_edges(img)

        # Convert chip mask to edges for line fitting using Canny
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        blurred = cv2.GaussianBlur(gray, (5, 5), 1.4)
        edges = cv2.Canny(blurred, 50, 150)

        # Mask the edges to only include chip area
        edges = cv2.bitwise_and(edges, chip_mask)

        result = {
            'edges': edges,
            'lines': [],
            'fitted_lines': {'horizontal': None, 'vertical': None},
            'algorithm_mode': algorithm_mode
        }

        if algorithm_mode == 'hough_only':
            # Hough-Only Mode: Use only Hough Transform with line grouping/averaging
            lines = self.fit_lines_hough(edges)
            if not hasattr(self, 'hough_fitter'):
                self.hough_fitter = HoughLineFitter(debug=getattr(self, 'debug', False))
            grouped_lines = self.hough_fitter.group_similar_lines(lines)

            # Average lines within groups
            horizontal_averaged = self.hough_fitter.average_line_groups(grouped_lines['horizontal'])
            vertical_averaged = self.hough_fitter.average_line_groups(grouped_lines['vertical'])

            result['lines'] = lines
            result['fitted_lines'] = {
                'horizontal': horizontal_averaged,
                'vertical': vertical_averaged
            }

        elif algorithm_mode == 'ransac_only':
            # RANSAC-Only Mode: Convert edge pixels directly to points and apply RANSAC
            if not hasattr(self, 'ransac_fitter'):
                self.ransac_fitter = RANSACLineFitter(debug=getattr(self, 'debug', False))
            points = self.ransac_fitter.edges_to_points(edges)
            fitted_lines = self.ransac_fitter.fit_lines(points)

            result['fitted_lines'] = fitted_lines

        elif algorithm_mode == 'parallel':
            # Parallel Processing: Run both algorithms independently
            # Hough Transform path
            hough_lines = self.fit_lines_hough(edges)
            hough_fitted = self.fit_lines_ransac(hough_lines) if hough_lines else {'horizontal': None, 'vertical': None}

            # Direct RANSAC path
            if not hasattr(self, 'ransac_fitter'):
                self.ransac_fitter = RANSACLineFitter(debug=getattr(self, 'debug', False))
            points = self.ransac_fitter.edges_to_points(edges)
            ransac_fitted = self.ransac_fitter.fit_lines(points)

            result['lines'] = hough_lines
            result['fitted_lines'] = {
                'hough_path': hough_fitted,
                'ransac_path': ransac_fitted
            }

        else:  # 'sequential' mode (default)
            # Sequential Processing: Apply Hough Transform first, then refine with RANSAC
            lines = self.fit_lines_hough(edges)
            if lines:
                fitted_lines = self.fit_lines_ransac(lines)
                result['lines'] = lines
                result['fitted_lines'] = fitted_lines

        return result


class NoEdgeDetector(EdgeDetector):
    """Dummy edge detector that assumes entire image is chip (for debugging)"""

    def __init__(self, debug=False):
        super().__init__(debug=debug)
        self.hough_fitter = HoughLineFitter(debug=debug)
        self.ransac_fitter = RANSACLineFitter(debug=debug)

    def detect_chip_edges(self, img):
        """Always return full image as chip"""
        h, w = img.shape[:2]
        return np.ones((h, w), np.uint8) * 255

    def is_on_chip(self, img, x, y, margin=10):
        """Always return True (entire image is chip)"""
        h, w = img.shape[:2]
        return 0 <= x < w and 0 <= y < h


class CannyEdgeDetector(EdgeDetector):
    """
    Edge detector that uses Canny edge detection for high-precision edge detection.
    Extracted from BackgroundEdgeDetector for better modularity and separation of concerns.
    """

    def __init__(self, debug=False):
        super().__init__(debug=debug)
        self.hough_fitter = HoughLineFitter(debug=debug)
        self.ransac_fitter = RANSACLineFitter(debug=debug)

    def detect_chip_edges(self, img):
        """
        Detect chip edges using Canny edge detection with rotation robustness.
        Returns: mask where True = chip area, False = outside chip
        """
        # Apply Canny edge detection
        edges = self.detect_chip_edges_canny(img)

        # Convert edges to chip mask using morphological operations
        return self._edges_to_chip_mask(edges, img.shape[:2])

    def detect_chip_edges_canny(self, img, low_threshold=50, high_threshold=150):
        """
        High-precision edge detection using Canny edge detector.

        Args:
            img: Input image
            low_threshold: Lower threshold for edge detection
            high_threshold: Upper threshold for edge detection

        Returns:
            edges: Binary edge image
        """
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 1.4)

        # Apply Canny edge detection
        edges = cv2.Canny(blurred, low_threshold, high_threshold)

        return edges

    def _edges_to_chip_mask(self, edges, img_shape):
        """
        Convert edge image to chip mask using improved morphological operations.

        This method uses a more robust approach that doesn't rely solely on flood-fill,
        making it work better with incomplete edge contours.

        Args:
            edges: Binary edge image from Canny detector
            img_shape: Shape of original image (h, w)

        Returns:
            chip_mask: Binary mask where True = chip area
        """
        h, w = img_shape

        # Method 1: Morphological approach for creating solid regions from edges
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (25, 25))

        # Step 1: Close small gaps in edges
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_medium)

        # Step 2: Dilate to create thicker edge regions
        edges_dilated = cv2.dilate(edges_closed, kernel_large, iterations=2)

        # Step 3: Try flood-fill approach for well-formed contours
        flood_filled = self._fill_enclosed_areas(edges_closed)

        # Step 4: Combine dilated edges with flood-filled areas
        # This ensures we get a mask even if flood-fill doesn't work perfectly
        combined_mask = cv2.bitwise_or(edges_dilated, flood_filled)

        # Step 5: Clean up the combined mask
        # Remove small noise
        cleaned_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel_small)
        # Fill small holes
        cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_CLOSE, kernel_medium)

        # Step 6: Apply more lenient size threshold for edge-based detection
        # Canny edges might not always form large solid regions, so be more permissive
        final_mask = self._extract_largest_component_lenient(cleaned_mask, h, w)

        return final_mask

    def _extract_largest_component_lenient(self, mask, h, w):
        """
        Extract the largest connected component with more lenient size requirements.

        This version is more permissive for edge-based detection where solid regions
        might be smaller than traditional chip detection methods.
        """
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(mask)
        if num_labels > 1:
            # Get the largest component (excluding background)
            areas = stats[1:, cv2.CC_STAT_AREA]
            largest_idx = 1 + np.argmax(areas)

            # More lenient threshold: accept if at least 5% of image (vs 15% in strict version)
            # This is more appropriate for edge-based detection
            if areas[largest_idx - 1] > 0.05 * h * w:
                final_mask = (labels == largest_idx).astype(np.uint8) * 255
            else:
                # If still too small, check if we have any reasonable amount of edge content
                chip_ratio = np.sum(mask > 0) / (h * w)
                if chip_ratio > 0.02:  # At least 2% of image has edge content
                    final_mask = mask
                else:
                    final_mask = np.zeros((h, w), np.uint8)
        else:
            # If no components found, check if original mask has any significant content
            chip_ratio = np.sum(mask > 0) / (h * w)
            if chip_ratio > 0.02:  # Very lenient threshold for edge-based detection
                final_mask = mask
            else:
                final_mask = np.zeros((h, w), np.uint8)

        return final_mask

    def _fill_enclosed_areas(self, edges):
        """
        Fill areas enclosed by edges to create solid regions.

        Args:
            edges: Binary edge image

        Returns:
            filled: Binary image with filled regions
        """
        # Create a copy for flood fill
        filled = edges.copy()
        h, w = filled.shape

        # Create mask for flood fill (slightly larger than image)
        mask = np.zeros((h + 2, w + 2), np.uint8)

        # Flood fill from corners to identify background
        cv2.floodFill(filled, mask, (0, 0), 255)
        cv2.floodFill(filled, mask, (w-1, 0), 255)
        cv2.floodFill(filled, mask, (0, h-1), 255)
        cv2.floodFill(filled, mask, (w-1, h-1), 255)

        # Invert to get chip areas
        chip_mask = cv2.bitwise_not(filled)

        return chip_mask

    def fit_lines_hough(self, edges, min_line_length=100, max_line_gap=10, threshold=50):
        """
        Detect lines using Probabilistic Hough Transform.
        Uses the independent HoughLineFitter class.

        Args:
            edges: Binary edge image from Canny detector
            min_line_length: Minimum length of line segments
            max_line_gap: Maximum gap between line segments
            threshold: Minimum number of votes for line detection

        Returns:
            lines: List of detected lines [(x1, y1, x2, y2), ...]
        """
        return self.hough_fitter.fit_lines(edges, min_line_length, max_line_gap, threshold)

    def fit_lines_ransac(self, lines, distance_threshold=5.0):
        """
        Robust line fitting using RANSAC algorithm.
        Uses the independent RANSACLineFitter class.

        Args:
            lines: List of line segments from Hough transform
            distance_threshold: Maximum distance for inliers

        Returns:
            Dict containing fitted lines for horizontal and vertical edges
        """
        return self.ransac_fitter.fit_lines(lines, distance_threshold)

    def detect_edges_with_line_fitting(self, img, algorithm_mode='ransac_only'):
        """
        Comprehensive edge detection using Canny + line fitting algorithms.
        Supports flexible algorithm combinations as specified in requirements.

        Args:
            img: Input image
            algorithm_mode: 'sequential', 'hough_only', 'ransac_only', or 'parallel'

        Returns:
            Dict with edge detection results and fitted lines
        """
        # Step 1: Canny edge detection
        edges = self.detect_chip_edges_canny(img)

        result = {
            'edges': edges,
            'lines': [],
            'fitted_lines': {'horizontal': None, 'vertical': None},
            'algorithm_mode': algorithm_mode
        }

        if algorithm_mode == 'hough_only':
            # Hough-Only Mode: Use only Hough Transform with line grouping/averaging
            lines = self.fit_lines_hough(edges)
            grouped_lines = self.hough_fitter.group_similar_lines(lines)

            # Average lines within groups
            horizontal_averaged = self.hough_fitter.average_line_groups(grouped_lines['horizontal'])
            vertical_averaged = self.hough_fitter.average_line_groups(grouped_lines['vertical'])

            result['lines'] = lines
            result['fitted_lines'] = {
                'horizontal': horizontal_averaged,
                'vertical': vertical_averaged
            }

        elif algorithm_mode == 'ransac_only':
            # RANSAC-Only Mode: Convert edge pixels directly to points and apply RANSAC
            points = self.ransac_fitter.edges_to_points(edges)
            fitted_lines = self.ransac_fitter.fit_lines(points)

            result['fitted_lines'] = fitted_lines

        elif algorithm_mode == 'parallel':
            # Parallel Processing: Run both algorithms independently
            # Hough Transform path
            hough_lines = self.fit_lines_hough(edges)
            hough_fitted = self.fit_lines_ransac(hough_lines) if hough_lines else {'horizontal': None, 'vertical': None}

            # Direct RANSAC path
            points = self.ransac_fitter.edges_to_points(edges)
            ransac_fitted = self.ransac_fitter.fit_lines(points)

            result['lines'] = hough_lines
            result['fitted_lines'] = {
                'hough_path': hough_fitted,
                'ransac_path': ransac_fitted
            }

        else:  # 'sequential' mode (default)
            # Sequential Processing: Apply Hough Transform first, then refine with RANSAC
            lines = self.fit_lines_hough(edges)
            if lines:
                fitted_lines = self.fit_lines_ransac(lines)
                result['lines'] = lines
                result['fitted_lines'] = fitted_lines

        return result


class BackgroundEdgeDetector(EdgeDetector):
    """
    Edge detector that focuses on background (beige/yellow tape) detection.
    Enhanced with rotation robustness.
    """

    def __init__(self, debug=False):
        super().__init__(debug=debug)
        self.hough_fitter = HoughLineFitter(debug=debug)
        self.ransac_fitter = RANSACLineFitter(debug=debug)

    def detect_chip_edges(self, img):
        """
        Detect chip by identifying background (beige/yellow) areas with rotation robustness
        """
        if len(img.shape) != 3:
            # Need color image for background detection
            return np.ones(img.shape[:2], np.uint8) * 255

        # Enhanced rotation-robust background detection
        return self._detect_background_rotation_robust(img)

    def _detect_background_rotation_robust(self, img):
        """Enhanced background detection that handles rotated chips"""
        # Convert to multiple color spaces for robustness
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)

        # Define multiple ranges for beige/yellow background in HSV
        hsv_ranges = [
            ([20, 30, 120], [35, 100, 255]),   # Typical beige
            ([15, 20, 150], [40, 80, 255]),    # Lighter beige/yellow
            ([10, 10, 140], [45, 120, 255]),   # Extended range
            ([0, 0, 180], [25, 40, 255]),      # Very light background
        ]


        # Combine HSV masks
        background_mask_hsv = np.zeros(hsv.shape[:2], dtype=np.uint8)
        for lower, upper in hsv_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            background_mask_hsv = cv2.bitwise_or(background_mask_hsv, mask)

        # Additional LAB-based detection for better color separation
        # Background typically has higher L (lightness) values
        l_channel = lab[:, :, 0]
        _, background_mask_lab = cv2.threshold(l_channel, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Combine HSV and LAB masks
        background_mask = cv2.bitwise_or(background_mask_hsv, background_mask_lab)

        # Clean up background mask with rotation-invariant operations
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (20, 20))

        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_OPEN, kernel_small)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_CLOSE, kernel_large)

        # Chip is NOT background
        chip_mask = cv2.bitwise_not(background_mask)

        # Fill small holes with circular kernels
        kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (40, 40))
        chip_mask = cv2.morphologyEx(chip_mask, cv2.MORPH_CLOSE, kernel_fill)

        # Keep only large connected components
        final_mask = self._filter_large_components(chip_mask)

        if self.debug:
            self._save_background_debug_images(img, background_mask, final_mask)

        return final_mask

    def _filter_large_components(self, chip_mask):
        """Filter to keep only large connected components"""
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(chip_mask)
        h, w = chip_mask.shape
        min_area = 0.08 * h * w  # At least 8% of image (more lenient for rotated chips)

        final_mask = np.zeros_like(chip_mask)
        for i in range(1, num_labels):
            if stats[i, cv2.CC_STAT_AREA] > min_area:
                final_mask[labels == i] = 255

        # If no large components, assume entire image is chip
        # if np.sum(final_mask) == 0:
        #     final_mask = np.ones((h, w), np.uint8) * 255

        return final_mask

    def _edges_to_chip_mask(self, edges, img_shape):
        """
        Convert edge image to chip mask using morphological operations.
        Adapted for BackgroundEdgeDetector.

        Args:
            edges: Binary edge image from edge detector
            img_shape: Shape of original image (h, w)

        Returns:
            chip_mask: Binary mask where True = chip area
        """
        h, w = img_shape

        # Use morphological operations to create chip mask from edges
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (20, 20))

        # Close gaps in edges
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_large)

        # Fill enclosed areas to create chip mask
        chip_mask = self._fill_enclosed_areas(edges_closed)

        # Clean up with morphological operations
        chip_mask = cv2.morphologyEx(chip_mask, cv2.MORPH_OPEN, kernel_small)
        chip_mask = cv2.morphologyEx(chip_mask, cv2.MORPH_CLOSE, kernel_large)

        # Keep only large connected components
        final_mask = self._extract_largest_component(chip_mask, h, w)

        return final_mask

    def _fill_enclosed_areas(self, edges):
        """
        Fill areas enclosed by edges to create solid regions.
        Adapted for BackgroundEdgeDetector.

        Args:
            edges: Binary edge image

        Returns:
            filled: Binary image with filled regions
        """
        # Create a copy for flood fill
        filled = edges.copy()
        h, w = filled.shape

        # Create mask for flood fill (slightly larger than image)
        mask = np.zeros((h + 2, w + 2), np.uint8)

        # Flood fill from corners to identify background
        cv2.floodFill(filled, mask, (0, 0), 255)
        cv2.floodFill(filled, mask, (w-1, 0), 255)
        cv2.floodFill(filled, mask, (0, h-1), 255)
        cv2.floodFill(filled, mask, (w-1, h-1), 255)

        # Invert to get chip areas
        chip_mask = cv2.bitwise_not(filled)

        return chip_mask

    def fit_lines_hough(self, edges, min_line_length=100, max_line_gap=10, threshold=50):
        """
        Detect lines using Probabilistic Hough Transform.
        Uses the independent HoughLineFitter class.

        Args:
            edges: Binary edge image from background edge detector
            min_line_length: Minimum length of line segments
            max_line_gap: Maximum gap between line segments
            threshold: Minimum number of votes for line detection

        Returns:
            lines: List of detected lines [(x1, y1, x2, y2), ...]
        """
        return self.hough_fitter.fit_lines(edges, min_line_length, max_line_gap, threshold)

    def fit_lines_ransac(self, lines, distance_threshold=5.0):
        """
        Robust line fitting using RANSAC algorithm.
        Uses the independent RANSACLineFitter class.

        Args:
            lines: List of line segments from Hough transform
            distance_threshold: Maximum distance for inliers

        Returns:
            Dict containing fitted lines for horizontal and vertical edges
        """
        return self.ransac_fitter.fit_lines(lines, distance_threshold)

    def detect_edges_with_line_fitting(self, img, algorithm_mode='ransac_only'):
        """
        Comprehensive edge detection using background detection + line fitting algorithms.
        Supports flexible algorithm combinations as specified in requirements.

        Args:
            img: Input image
            algorithm_mode: 'sequential', 'hough_only', 'ransac_only', or 'parallel'

        Returns:
            Dict with edge detection results and fitted lines
        """
        # Step 1: Background-based edge detection
        # Use Canny on the chip mask to get edges for line fitting
        chip_mask = self.detect_chip_edges(img)

        # Convert chip mask to edges using Canny
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        blurred = cv2.GaussianBlur(gray, (5, 5), 1.4)
        edges = cv2.Canny(blurred, 50, 150)

        # Mask the edges to only include chip area
        edges = cv2.bitwise_and(edges, chip_mask)

        result = {
            'edges': edges,
            'lines': [],
            'fitted_lines': {'horizontal': None, 'vertical': None},
            'algorithm_mode': algorithm_mode
        }

        if algorithm_mode == 'hough_only':
            # Hough-Only Mode: Use only Hough Transform with line grouping/averaging
            lines = self.fit_lines_hough(edges)
            grouped_lines = self.hough_fitter.group_similar_lines(lines)

            # Average lines within groups
            horizontal_averaged = self.hough_fitter.average_line_groups(grouped_lines['horizontal'])
            vertical_averaged = self.hough_fitter.average_line_groups(grouped_lines['vertical'])

            result['lines'] = lines
            result['fitted_lines'] = {
                'horizontal': horizontal_averaged,
                'vertical': vertical_averaged
            }

        elif algorithm_mode == 'ransac_only':
            # RANSAC-Only Mode: Convert edge pixels directly to points and apply RANSAC
            points = self.ransac_fitter.edges_to_points(edges)
            fitted_lines = self.ransac_fitter.fit_lines(points)

            result['fitted_lines'] = fitted_lines

        elif algorithm_mode == 'parallel':
            # Parallel Processing: Run both algorithms independently
            # Hough Transform path
            hough_lines = self.fit_lines_hough(edges)
            hough_fitted = self.fit_lines_ransac(hough_lines) if hough_lines else {'horizontal': None, 'vertical': None}

            # Direct RANSAC path
            points = self.ransac_fitter.edges_to_points(edges)
            ransac_fitted = self.ransac_fitter.fit_lines(points)

            result['lines'] = hough_lines
            result['fitted_lines'] = {
                'hough_path': hough_fitted,
                'ransac_path': ransac_fitted
            }

        else:  # 'sequential' mode (default)
            # Sequential Processing: Apply Hough Transform first, then refine with RANSAC
            lines = self.fit_lines_hough(edges)
            if lines:
                fitted_lines = self.fit_lines_ransac(lines)
                result['lines'] = lines
                result['fitted_lines'] = fitted_lines

        return result

    def _save_background_debug_images(self, img, background_mask, final_mask):
        """Save debug images for background detection"""
        timestamp = int(time.time())
        debug_img = img.copy()
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)
        cv2.imwrite(f"background_debug_{timestamp}.png", debug_img)