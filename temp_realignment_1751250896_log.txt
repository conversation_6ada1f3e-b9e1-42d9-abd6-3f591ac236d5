================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:34:56
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751250896.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751243640.json
================================================================================

[2025-06-30 10:34:56.133] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:34:56.145] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:34:56.154] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:34:56.281] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:34:56.290] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:34:56.301] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:34:56.310] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:34:56.320] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:34:56.329] [INFO] [STATUS] [ChipAlign] Using BackgroundEdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:34:56.339] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:34:56.480] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:34:56.490] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:34:56.499] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:34:56.513] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:34:56.557] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:34:57.861] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 10:34:59.700] [INFO] [POSITION] Position feedback: (349.61, 0.00) μm
[2025-06-30 10:35:01.500] [INFO] [POSITION] Position feedback: (695.40, 0.00) μm
[2025-06-30 10:35:03.300] [INFO] [POSITION] Position feedback: (1046.49, 0.00) μm
[2025-06-30 10:35:05.080] [INFO] [POSITION] Position feedback: (1386.36, 0.00) μm
[2025-06-30 10:35:06.810] [INFO] [POSITION] Position feedback: (1732.16, 0.00) μm
[2025-06-30 10:35:08.600] [INFO] [POSITION] Position feedback: (2090.87, 0.00) μm
[2025-06-30 10:35:10.400] [INFO] [POSITION] Position feedback: (2423.33, 0.00) μm
[2025-06-30 10:35:12.180] [INFO] [POSITION] Position feedback: (2774.42, 0.00) μm
[2025-06-30 10:35:13.869] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:35:14.019] [INFO] [POSITION] Position feedback: (3110.27, 0.00) μm
[2025-06-30 10:35:14.231] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:35:15.387] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:35:15.560] [INFO] [POSITION] Position feedback: (3110.27, 0.00) μm
[2025-06-30 10:35:16.070] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 3110.3) μm
[2025-06-30 10:35:16.081] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 3110.31) μm
[2025-06-30 10:35:16.091] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:35:16.102] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:35:16.112] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 3110.31) μm
[2025-06-30 10:35:16.122] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:35:16.132] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:35:16.143] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:35:16.152] [INFO] [STATUS] Translation: (0.00, 3110.31) μm
[2025-06-30 10:35:16.164] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:35:16.175] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:35:16.185] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:35:16.195] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:35:16.205] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:35:16.222] [INFO] [SUCCESS] Successfully transformed 3 flakes
