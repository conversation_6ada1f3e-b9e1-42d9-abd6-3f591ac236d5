================================================================================
SCANNING OPERATION LOG - 2025-06-30 12:01:31
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751256090\scan_adaptive_1751256091.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751256090
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751256090\debug_screenshots
================================================================================

[2025-06-30 12:01:31.816] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 12:01:31.838] [INFO] [SYSTEM] Using custom scan folder: scan_1751256090
[2025-06-30 12:01:31.850] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 12:01:31.966] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 12:01:31.976] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 12:01:32.080] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 12:01:32.456] [INFO] [POSITION] Position feedback: (9.73, 0.00) μm
[2025-06-30 12:01:33.326] [INFO] [POSITION] Position feedback: (362.52, 0.00) μm
[2025-06-30 12:01:34.064] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 12:01:34.225] [INFO] [POSITION] Position feedback: (690.75, 0.00) μm
[2025-06-30 12:01:34.438] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 12:01:34.665] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 12:01:34.806] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 12:01:35.317] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-06-30 12:01:35.328] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 12:01:35.436] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 12:01:35.447] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 12:01:35.476] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 12:01:35.486] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 12:01:35.565] [INFO] [STATUS] ✗ Validation failed: Missing line orientations (H:False, V:False)
[2025-06-30 12:01:35.576] [INFO] [STATUS] ✗ CRITICAL: Edge detection validation failed
[2025-06-30 12:01:35.585] [INFO] [STATUS]   → Detected edges do not meet quality requirements
[2025-06-30 12:01:35.595] [INFO] [STATUS]   → Insufficient line fitting or poor edge quality
[2025-06-30 12:01:35.606] [ERROR] [ERROR] ✗ CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 12:01:35.616] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 12:01:35.628] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 12:01:35.640] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 12:01:35.650] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 12:01:36.006] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 12:01:36.017] [INFO] [STATUS] Row 0: Reached right edge at column 0
[2025-06-30 12:01:36.027] [INFO] [STATUS] Row 0 complete: 0 positions scanned
[2025-06-30 12:01:36.037] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 12:01:36.186] [INFO] [POSITION] Position feedback: (9.73, -4.23) μm
[2025-06-30 12:01:39.545] [INFO] [WORKFLOW] Finding left edge for row (rotation-robust)
