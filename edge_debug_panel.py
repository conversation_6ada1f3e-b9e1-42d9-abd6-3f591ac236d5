#!/usr/bin/env python3
"""
Real-time Edge Detection Debug Panel

This module provides a debug panel/window that displays real-time edge detection
results during the scanning process to help diagnose premature scan termination
issues caused by incorrect edge detection.
"""

import cv2
import numpy as np
import time
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QCheckBox, QScrollArea, QTextEdit,
                            QSplitter, QFrame, QGridLayout)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QPixmap, QImage, QFont

from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector


class EdgeDetectionDebugPanel(QWidget):
    """
    Real-time debug panel for edge detection visualization during scanning.
    
    Features:
    - Real-time display of captured images
    - Edge detection mask overlays
    - Line fitting results visualization
    - On-chip/off-chip status indicators
    - Timestamp and position information
    - Scrollable log of debug events
    """
    
    # Signals for communication with scanning thread
    update_debug_info = pyqtSignal(dict)  # Signal to update debug information
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Edge Detection Debug Panel")
        self.setMinimumSize(1200, 800)
        
        # Debug state
        self.enabled = False
        self.current_detector = None
        self.debug_history = []
        self.max_history_items = 100
        
        # Initialize UI
        self.init_ui()
        
        # Connect signals
        self.update_debug_info.connect(self._update_display)
        
        # Timer for periodic updates (if needed)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._periodic_update)
        
    def init_ui(self):
        """Initialize the debug panel UI"""
        main_layout = QVBoxLayout()
        
        # Control panel
        control_layout = QHBoxLayout()
        
        self.enable_checkbox = QCheckBox("Enable Debug Panel")
        self.enable_checkbox.stateChanged.connect(self._toggle_debug)
        control_layout.addWidget(self.enable_checkbox)
        
        self.clear_button = QPushButton("Clear History")
        self.clear_button.clicked.connect(self._clear_history)
        control_layout.addWidget(self.clear_button)
        
        self.save_button = QPushButton("Save Debug Images")
        self.save_button.clicked.connect(self._save_debug_images)
        control_layout.addWidget(self.save_button)
        
        control_layout.addStretch()
        
        # Status label
        self.status_label = QLabel("Debug Panel: Disabled")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; padding: 5px; }")
        control_layout.addWidget(self.status_label)
        
        main_layout.addLayout(control_layout)
        
        # Main content splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side: Image displays
        image_widget = self._create_image_display_widget()
        splitter.addWidget(image_widget)
        
        # Right side: Information panel
        info_widget = self._create_info_panel_widget()
        splitter.addWidget(info_widget)
        
        # Set splitter proportions
        splitter.setSizes([800, 400])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
    def _create_image_display_widget(self):
        """Create the image display widget"""
        widget = QWidget()
        layout = QGridLayout()
        
        # Original image
        self.original_label = QLabel("Original Image")
        self.original_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.original_label.setMinimumSize(300, 200)
        self.original_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
        layout.addWidget(QLabel("Original Image:"), 0, 0)
        layout.addWidget(self.original_label, 1, 0)
        
        # Edge detection mask
        self.mask_label = QLabel("Edge Detection Mask")
        self.mask_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.mask_label.setMinimumSize(300, 200)
        self.mask_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
        layout.addWidget(QLabel("Edge Detection Mask:"), 0, 1)
        layout.addWidget(self.mask_label, 1, 1)
        
        # Canny edges (if applicable)
        self.edges_label = QLabel("Canny Edges")
        self.edges_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.edges_label.setMinimumSize(300, 200)
        self.edges_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
        layout.addWidget(QLabel("Canny Edges:"), 2, 0)
        layout.addWidget(self.edges_label, 3, 0)
        
        # Line fitting results
        self.lines_label = QLabel("Line Fitting Results")
        self.lines_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lines_label.setMinimumSize(300, 200)
        self.lines_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: #f0f0f0; }")
        layout.addWidget(QLabel("Line Fitting Results:"), 2, 1)
        layout.addWidget(self.lines_label, 3, 1)
        
        widget.setLayout(layout)
        return widget
        
    def _create_info_panel_widget(self):
        """Create the information panel widget"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Current status
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_layout = QVBoxLayout()
        
        self.detector_label = QLabel("Detector: None")
        self.position_label = QLabel("Position: Unknown")
        self.timestamp_label = QLabel("Last Update: Never")
        
        font = QFont()
        font.setPointSize(10)
        for label in [self.detector_label, self.position_label, self.timestamp_label]:
            label.setFont(font)
            status_layout.addWidget(label)
        
        status_frame.setLayout(status_layout)
        layout.addWidget(QLabel("Current Status:"))
        layout.addWidget(status_frame)
        
        # On-chip status indicators
        indicators_frame = QFrame()
        indicators_frame.setFrameStyle(QFrame.Shape.Box)
        indicators_layout = QVBoxLayout()
        
        self.center_indicator = QLabel("Center: Unknown")
        self.left_indicator = QLabel("Left 20%: Unknown")
        self.right_indicator = QLabel("Right 80%: Unknown")
        self.top_indicator = QLabel("Top 20%: Unknown")
        self.bottom_indicator = QLabel("Bottom 80%: Unknown")
        
        for indicator in [self.center_indicator, self.left_indicator, self.right_indicator,
                         self.top_indicator, self.bottom_indicator]:
            indicator.setFont(font)
            indicators_layout.addWidget(indicator)
        
        indicators_frame.setLayout(indicators_layout)
        layout.addWidget(QLabel("On-Chip Status:"))
        layout.addWidget(indicators_frame)
        
        # Debug log
        layout.addWidget(QLabel("Debug Log:"))
        self.debug_log = QTextEdit()
        self.debug_log.setMaximumHeight(200)
        self.debug_log.setReadOnly(True)
        layout.addWidget(self.debug_log)
        
        widget.setLayout(layout)
        return widget
        
    def _toggle_debug(self, state):
        """Toggle debug panel enabled/disabled state"""
        self.enabled = state == Qt.CheckState.Checked.value
        
        if self.enabled:
            self.status_label.setText("Debug Panel: Enabled")
            self.status_label.setStyleSheet("QLabel { font-weight: bold; padding: 5px; background-color: #d4edda; }")
            self.update_timer.start(1000)  # Update every second
            self._log_debug("Debug panel enabled")
        else:
            self.status_label.setText("Debug Panel: Disabled")
            self.status_label.setStyleSheet("QLabel { font-weight: bold; padding: 5px; }")
            self.update_timer.stop()
            self._log_debug("Debug panel disabled")
    
    def _clear_history(self):
        """Clear debug history and displays"""
        self.debug_history.clear()
        self.debug_log.clear()
        
        # Clear image displays
        for label in [self.original_label, self.mask_label, self.edges_label, self.lines_label]:
            label.clear()
            label.setText(label.objectName() or "No Data")
        
        self._log_debug("Debug history cleared")
    
    def _save_debug_images(self):
        """Save current debug images to files"""
        if not self.debug_history:
            self._log_debug("No debug images to save")
            return
        
        timestamp = int(time.time())
        latest_debug = self.debug_history[-1]
        
        try:
            # Save original image
            if 'original_image' in latest_debug:
                cv2.imwrite(f"debug_original_{timestamp}.png", latest_debug['original_image'])
            
            # Save mask
            if 'mask' in latest_debug:
                cv2.imwrite(f"debug_mask_{timestamp}.png", latest_debug['mask'])
            
            # Save edges
            if 'edges' in latest_debug:
                cv2.imwrite(f"debug_edges_{timestamp}.png", latest_debug['edges'])
            
            self._log_debug(f"Debug images saved with timestamp {timestamp}")
            
        except Exception as e:
            self._log_debug(f"Error saving debug images: {str(e)}")
    
    def _log_debug(self, message):
        """Add a message to the debug log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.debug_log.append(log_entry)
        
        # Auto-scroll to bottom
        scrollbar = self.debug_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def _periodic_update(self):
        """Periodic update function (if needed)"""
        # This can be used for any periodic updates when debug is enabled
        pass

    def update_debug_data(self, img, detector, position_info=None):
        """
        Update debug panel with new edge detection data.
        This method is called from the scanning thread.

        Args:
            img: Current captured image
            detector: Edge detector instance being used
            position_info: Optional position information dict
        """
        if not self.enabled:
            return

        try:
            # Store current detector
            self.current_detector = detector

            # Perform edge detection analysis
            debug_data = self._analyze_edge_detection(img, detector, position_info)

            # Emit signal to update UI (thread-safe)
            self.update_debug_info.emit(debug_data)

        except Exception as e:
            self._log_debug(f"Error updating debug data: {str(e)}")

    def _analyze_edge_detection(self, img, detector, position_info):
        """
        Analyze edge detection results and prepare debug data.

        Args:
            img: Input image
            detector: Edge detector instance
            position_info: Position information

        Returns:
            dict: Debug data for display
        """
        debug_data = {
            'timestamp': time.time(),
            'detector_type': type(detector).__name__,
            'position_info': position_info or {},
            'original_image': img.copy()
        }

        try:
            # Basic edge detection
            mask = detector.detect_chip_edges(img)
            debug_data['mask'] = mask

            # Test key points for on-chip status
            h, w = img.shape[:2]
            test_points = [
                (w // 2, h // 2, "Center"),
                (int(w * 0.2), h // 2, "Left 20%"),
                (int(w * 0.8), h // 2, "Right 80%"),
                (w // 2, int(h * 0.2), "Top 20%"),
                (w // 2, int(h * 0.8), "Bottom 80%")
            ]

            on_chip_status = {}
            for x, y, label in test_points:
                on_chip = detector.is_on_chip(img, x, y)
                on_chip_status[label] = on_chip

            debug_data['on_chip_status'] = on_chip_status

            # Additional analysis for CannyEdgeDetector
            if isinstance(detector, CannyEdgeDetector):
                try:
                    # Get Canny edges
                    edges = detector.detect_chip_edges_canny(img)
                    debug_data['edges'] = edges

                    # Get line fitting results
                    edge_result = detector.detect_edges_with_line_fitting(img, algorithm_mode='sequential')
                    debug_data['edge_result'] = edge_result

                    # Create visualization of line fitting
                    lines_viz = self._create_lines_visualization(img, edge_result)
                    debug_data['lines_visualization'] = lines_viz

                except Exception as e:
                    debug_data['canny_error'] = str(e)

        except Exception as e:
            debug_data['analysis_error'] = str(e)

        return debug_data

    def _create_lines_visualization(self, img, edge_result):
        """
        Create visualization of line fitting results.

        Args:
            img: Original image
            edge_result: Edge detection result with line fitting

        Returns:
            numpy.ndarray: Visualization image
        """
        try:
            # Create a copy of the original image for visualization
            viz_img = img.copy()

            # Draw detected lines
            lines = edge_result.get('lines', [])
            for line in lines:
                if len(line) >= 4:
                    x1, y1, x2, y2 = line[:4]
                    cv2.line(viz_img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

            # Draw fitted lines if available
            fitted_lines = edge_result.get('fitted_lines', {})

            # Draw horizontal fitted line
            if fitted_lines.get('horizontal'):
                h_line = fitted_lines['horizontal']
                if isinstance(h_line, dict) and 'slope' in h_line:
                    # Draw line across image width
                    h, w = img.shape[:2]
                    slope = h_line['slope']
                    intercept = h_line['intercept']
                    y1 = int(slope * 0 + intercept)
                    y2 = int(slope * (w-1) + intercept)
                    cv2.line(viz_img, (0, y1), (w-1, y2), (255, 0, 0), 3)  # Blue for horizontal

            # Draw vertical fitted line
            if fitted_lines.get('vertical'):
                v_line = fitted_lines['vertical']
                if isinstance(v_line, dict) and 'slope' in v_line:
                    # For vertical lines: x = my + b
                    h, w = img.shape[:2]
                    slope = v_line['slope']
                    intercept = v_line['intercept']
                    x1 = int(slope * 0 + intercept)
                    x2 = int(slope * (h-1) + intercept)
                    cv2.line(viz_img, (x1, 0), (x2, h-1), (0, 0, 255), 3)  # Red for vertical

            return viz_img

        except Exception as e:
            # Return original image if visualization fails
            return img.copy()

    def _update_display(self, debug_data):
        """
        Update the debug panel display with new data.
        This method runs in the main UI thread.

        Args:
            debug_data: Debug data dict from _analyze_edge_detection
        """
        try:
            # Update status information
            detector_type = debug_data.get('detector_type', 'Unknown')
            self.detector_label.setText(f"Detector: {detector_type}")

            position_info = debug_data.get('position_info', {})
            if position_info:
                pos_text = f"Position: ({position_info.get('x', '?')}, {position_info.get('y', '?')})"
            else:
                pos_text = "Position: Unknown"
            self.position_label.setText(pos_text)

            timestamp = debug_data.get('timestamp', time.time())
            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
            self.timestamp_label.setText(f"Last Update: {time_str}")

            # Update on-chip status indicators
            on_chip_status = debug_data.get('on_chip_status', {})
            indicators = [
                (self.center_indicator, "Center"),
                (self.left_indicator, "Left 20%"),
                (self.right_indicator, "Right 80%"),
                (self.top_indicator, "Top 20%"),
                (self.bottom_indicator, "Bottom 80%")
            ]

            for indicator, key in indicators:
                status = on_chip_status.get(key, None)
                if status is True:
                    indicator.setText(f"{key}: ON chip")
                    indicator.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                elif status is False:
                    indicator.setText(f"{key}: OFF chip")
                    indicator.setStyleSheet("QLabel { color: red; font-weight: bold; }")
                else:
                    indicator.setText(f"{key}: Unknown")
                    indicator.setStyleSheet("QLabel { color: gray; }")

            # Update image displays
            self._update_image_displays(debug_data)

            # Add to history
            self.debug_history.append(debug_data)
            if len(self.debug_history) > self.max_history_items:
                self.debug_history.pop(0)

            # Log the update
            error_msg = debug_data.get('analysis_error') or debug_data.get('canny_error')
            if error_msg:
                self._log_debug(f"Analysis error: {error_msg}")
            else:
                on_chip_count = sum(1 for status in on_chip_status.values() if status)
                self._log_debug(f"Updated: {detector_type}, {on_chip_count}/5 points on-chip")

        except Exception as e:
            self._log_debug(f"Error updating display: {str(e)}")

    def _update_image_displays(self, debug_data):
        """Update the image display labels with new images"""
        try:
            # Update original image
            if 'original_image' in debug_data:
                pixmap = self._cv_image_to_pixmap(debug_data['original_image'])
                if pixmap:
                    self.original_label.setPixmap(pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio))

            # Update mask
            if 'mask' in debug_data:
                pixmap = self._cv_image_to_pixmap(debug_data['mask'])
                if pixmap:
                    self.mask_label.setPixmap(pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio))

            # Update edges (if available)
            if 'edges' in debug_data:
                pixmap = self._cv_image_to_pixmap(debug_data['edges'])
                if pixmap:
                    self.edges_label.setPixmap(pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio))

            # Update lines visualization (if available)
            if 'lines_visualization' in debug_data:
                pixmap = self._cv_image_to_pixmap(debug_data['lines_visualization'])
                if pixmap:
                    self.lines_label.setPixmap(pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio))

        except Exception as e:
            self._log_debug(f"Error updating image displays: {str(e)}")

    def _cv_image_to_pixmap(self, cv_img):
        """Convert OpenCV image to QPixmap for display"""
        try:
            if cv_img is None:
                return None

            # Handle different image types
            if len(cv_img.shape) == 3:
                # Color image
                height, width, channel = cv_img.shape
                bytes_per_line = 3 * width
                q_image = QImage(cv_img.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
            else:
                # Grayscale image
                height, width = cv_img.shape
                bytes_per_line = width
                q_image = QImage(cv_img.data, width, height, bytes_per_line, QImage.Format.Format_Grayscale8)

            return QPixmap.fromImage(q_image)

        except Exception as e:
            return None
