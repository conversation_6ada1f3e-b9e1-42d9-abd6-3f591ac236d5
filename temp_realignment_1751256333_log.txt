================================================================================
SCANNING OPERATION LOG - 2025-06-30 12:05:33
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751256333.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/scan_1751256237/auto_chip_reference_1751256245.json
================================================================================

[2025-06-30 12:05:33.862] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 12:05:33.873] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 12:05:33.883] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 12:05:33.988] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 12:05:33.998] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 12:05:34.010] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 12:05:34.020] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 12:05:34.029] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 12:05:34.038] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 12:05:34.050] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 12:05:34.248] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 12:05:34.258] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 12:05:34.268] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 12:05:34.278] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 12:05:34.322] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 12:05:35.888] [INFO] [POSITION] Position feedback: (16.93, 0.00) μm
[2025-06-30 12:05:37.877] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-06-30 12:05:39.787] [INFO] [POSITION] Position feedback: (349.82, 0.00) μm
[2025-06-30 12:05:41.787] [INFO] [POSITION] Position feedback: (522.51, 0.00) μm
[2025-06-30 12:05:43.797] [INFO] [POSITION] Position feedback: (695.40, 0.00) μm
[2025-06-30 12:05:45.777] [INFO] [POSITION] Position feedback: (868.09, 0.00) μm
[2025-06-30 12:05:47.757] [INFO] [POSITION] Position feedback: (1054.32, 0.00) μm
[2025-06-30 12:05:49.787] [INFO] [POSITION] Position feedback: (1236.74, 0.00) μm
[2025-06-30 12:05:51.679] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 12:05:51.807] [INFO] [POSITION] Position feedback: (1382.34, 0.00) μm
[2025-06-30 12:05:52.018] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 12:05:53.477] [INFO] [POSITION] Position feedback: (1382.34, 4.23) μm
[2025-06-30 12:05:55.597] [INFO] [POSITION] Position feedback: (1382.34, 132.05) μm
[2025-06-30 12:05:57.587] [INFO] [POSITION] Position feedback: (1382.34, 249.30) μm
[2025-06-30 12:05:59.516] [INFO] [POSITION] Position feedback: (1382.34, 371.83) μm
[2025-06-30 12:06:01.273] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 12:06:01.416] [INFO] [POSITION] Position feedback: (1382.34, 490.34) μm
[2025-06-30 12:06:01.928] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (490.4, 1382.4) μm
[2025-06-30 12:06:02.214] [INFO] [STATUS] [ChipAlign] Corner screenshot saved: debug_screenshots\corner_realignment_1751256361.png
[2025-06-30 12:06:02.240] [INFO] [STATUS] [ChipAlign] Corner position: (490.37, 1382.36) μm
[2025-06-30 12:06:02.251] [INFO] [STATUS] [ChipAlign] Current chip origin: (490.37, 1382.36) μm
[2025-06-30 12:06:02.260] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 12:06:02.270] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 12:06:02.280] [INFO] [STATUS] [ChipAlign] Translation: (490.37, 1382.36) μm
[2025-06-30 12:06:02.290] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 12:06:02.301] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 12:06:02.311] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 12:06:02.321] [INFO] [STATUS] Translation: (490.37, 1382.36) μm
[2025-06-30 12:06:02.331] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 12:06:02.341] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 12:06:02.349] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 12:06:02.360] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 12:06:02.374] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/scan_1751256237/scan_adaptive_1751256241.csv
[2025-06-30 12:06:02.391] [INFO] [SUCCESS] Successfully transformed 1 flakes
