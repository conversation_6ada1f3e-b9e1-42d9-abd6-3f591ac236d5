================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:53:08
================================================================================
Mode: adaptive
Output CSV: Z:/A.Members/张恩浩/python/transfer/test.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
================================================================================

[2025-06-30 10:53:08.377] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:53:08.390] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 10:53:08.491] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 10:53:08.502] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 10:53:08.600] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 10:53:08.982] [INFO] [POSITION] Position feedback: (4.44, 0.00) μm
[2025-06-30 10:53:09.831] [INFO] [POSITION] Position feedback: (349.18, 0.00) μm
[2025-06-30 10:53:10.732] [INFO] [POSITION] Position feedback: (717.84, 0.00) μm
[2025-06-30 10:53:11.612] [INFO] [POSITION] Position feedback: (1046.28, 0.00) μm
[2025-06-30 10:53:12.501] [INFO] [POSITION] Position feedback: (1386.36, 0.00) μm
[2025-06-30 10:53:13.222] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 10:53:13.361] [INFO] [POSITION] Position feedback: (1727.51, 0.00) μm
[2025-06-30 10:53:13.572] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 10:53:13.789] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 10:53:13.942] [INFO] [POSITION] Position feedback: (1727.93, 0.00) μm
[2025-06-30 10:53:14.453] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 1727.9) μm
[2025-06-30 10:53:14.463] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 10:53:14.611] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 10:53:14.621] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 10:53:14.659] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 10:53:14.669] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 10:53:14.916] [INFO] [SUCCESS] ✓ Edge detection validation passed (edges: 1394, lines: H+V)
[2025-06-30 10:53:14.926] [INFO] [STATUS] Creating chip boundary reference from detected edges...
[2025-06-30 10:53:14.936] [INFO] [STATUS] [ChipAlign] Creating chip boundary reference from edge detection results...
[2025-06-30 10:53:15.011] [WARNING] [WARNING] [ChipAlign] Warning: Could not get stage position, using (0,0): tuple indices must be integers or slices, not str
[2025-06-30 10:53:15.022] [INFO] [SUCCESS] [ChipAlign] ✓ Chip boundary reference created successfully
[2025-06-30 10:53:15.032] [INFO] [SUCCESS] ✓ Automatic chip boundary reference created successfully
[2025-06-30 10:53:15.051] [INFO] [SUCCESS] ✓ Reference saved to: auto_chip_reference_1751251995.json
[2025-06-30 10:53:15.061] [INFO] [SUCCESS] ✓ MANDATORY chip boundary reference creation completed successfully
[2025-06-30 10:53:15.072] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 10:53:15.083] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 10:53:15.094] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 10:53:18.570] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 10:53:18.583] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 10:53:18.721] [INFO] [POSITION] Position feedback: (-4.23, 0.00) μm
