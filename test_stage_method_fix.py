#!/usr/bin/env python3
"""
Test script to verify that the stage method name fix is working correctly.
This specifically tests that move_absolute is used instead of move_to.
"""

import sys
from unittest.mock import Mock

# Import the functions
from chip_alignment import ChipAlignmentSystem

def test_stage_method_name_fix():
    """Test that the re-alignment uses move_absolute instead of move_to."""
    print("=== Testing Stage Method Name Fix ===")
    
    try:
        # Create mock stage controller that tracks method calls
        mock_stage = Mock()
        mock_stage.get_position.return_value = {'x': 150.0, 'y': 250.0}
        mock_stage.set_zero = Mock()
        
        # Track which methods are called
        method_calls = []
        
        def track_move_absolute(y_um, x_um):
            method_calls.append(('move_absolute', y_um, x_um))
            print(f"  ✅ move_absolute called with: y_um={y_um:.2f}, x_um={x_um:.2f}")
        
        def track_move_to(*args, **kwargs):
            method_calls.append(('move_to', args, kwargs))
            print(f"  ❌ move_to called (should not happen): args={args}, kwargs={kwargs}")
        
        mock_stage.move_absolute = Mock(side_effect=track_move_absolute)
        mock_stage.move_to = Mock(side_effect=track_move_to)
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        # Mock the corner finding to avoid actual edge detection
        def mock_find_corner(edge_detector):
            return (105.0, 155.0)
        
        alignment_system._find_current_upper_left_corner = mock_find_corner
        
        # Create test reference data
        reference_data = {
            'boundary_reference': {'chip_origin_abs': [100.0, 150.0]},
            'relative_flakes': [],
            'edge_detection_method': 'CannyEdgeDetector'
        }
        
        print("\n--- Testing Re-alignment Method Calls ---")
        
        # Perform re-alignment
        result = alignment_system.perform_realignment(reference_data)
        
        # Verify the method calls
        print(f"\n--- Verifying Method Calls ---")
        
        # Check that move_absolute was called
        move_absolute_calls = [call for call in method_calls if call[0] == 'move_absolute']
        move_to_calls = [call for call in method_calls if call[0] == 'move_to']
        
        if move_absolute_calls:
            print(f"✅ move_absolute was called {len(move_absolute_calls)} time(s)")
            
            # Verify the parameters
            call = move_absolute_calls[0]
            y_um, x_um = call[1], call[2]
            
            # Expected: move_absolute(150.0, 100.0) because move_absolute takes (y_um, x_um)
            # and our reference position is (100.0, 150.0) which means x=100.0, y=150.0
            if y_um == 150.0 and x_um == 100.0:
                print(f"✅ move_absolute called with correct parameters: y_um={y_um}, x_um={x_um}")
            else:
                print(f"❌ move_absolute called with wrong parameters: y_um={y_um}, x_um={x_um}")
                print(f"   Expected: y_um=150.0, x_um=100.0")
                return False
        else:
            print(f"❌ move_absolute was NOT called")
            return False
        
        if move_to_calls:
            print(f"❌ move_to was called {len(move_to_calls)} time(s) (should not happen)")
            return False
        else:
            print(f"✅ move_to was NOT called (correct)")
        
        # Check that re-alignment succeeded
        if result.get('success'):
            print(f"✅ Re-alignment completed successfully")
        else:
            print(f"❌ Re-alignment failed: {result.get('error')}")
            return False
        
        print(f"\n🎉 Stage method name fix is working correctly!")
        print(f"✅ Uses move_absolute instead of move_to")
        print(f"✅ Correct parameter order: (y_um, x_um)")
        print(f"✅ Re-alignment workflow completes successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_order():
    """Test that the parameter order for move_absolute is correct."""
    print(f"\n=== Testing Parameter Order ===")
    
    try:
        # Create mock stage
        mock_stage = Mock()
        mock_stage.get_position.return_value = {'x': 150.0, 'y': 250.0}
        mock_stage.set_zero = Mock()
        
        # Track the exact parameters passed
        captured_params = []
        
        def capture_move_absolute(y_um, x_um):
            captured_params.append({'y_um': y_um, 'x_um': x_um})
        
        mock_stage.move_absolute = Mock(side_effect=capture_move_absolute)
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: None
        )
        
        # Mock corner finding
        alignment_system._find_current_upper_left_corner = lambda ed: (105.0, 155.0)
        
        # Test different reference positions
        test_cases = [
            {'name': 'Position (100, 150)', 'pos': [100.0, 150.0], 'expected_y': 150.0, 'expected_x': 100.0},
            {'name': 'Position (200, 300)', 'pos': [200.0, 300.0], 'expected_y': 300.0, 'expected_x': 200.0},
            {'name': 'Position (50, 75)', 'pos': [50.0, 75.0], 'expected_y': 75.0, 'expected_x': 50.0}
        ]
        
        for test_case in test_cases:
            print(f"\n--- Testing {test_case['name']} ---")
            
            reference_data = {
                'boundary_reference': {'chip_origin_abs': test_case['pos']},
                'relative_flakes': [],
                'edge_detection_method': 'CannyEdgeDetector'
            }
            
            captured_params.clear()
            result = alignment_system.perform_realignment(reference_data)
            
            if captured_params:
                actual_y = captured_params[0]['y_um']
                actual_x = captured_params[0]['x_um']
                expected_y = test_case['expected_y']
                expected_x = test_case['expected_x']
                
                if actual_y == expected_y and actual_x == expected_x:
                    print(f"✅ Correct parameters: y_um={actual_y}, x_um={actual_x}")
                else:
                    print(f"❌ Wrong parameters: got y_um={actual_y}, x_um={actual_x}")
                    print(f"   Expected: y_um={expected_y}, x_um={expected_x}")
                    return False
            else:
                print(f"❌ No move_absolute calls captured")
                return False
        
        print(f"\n✅ Parameter order is correct for all test cases!")
        return True
        
    except Exception as e:
        print(f"❌ Parameter order test failed: {str(e)}")
        return False

def main():
    """Run all stage method fix tests."""
    print("Testing Stage Method Name Fix")
    print("=" * 60)
    
    tests = [
        test_stage_method_name_fix,
        test_parameter_order
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            print(f"✗ {test.__name__} CRASHED: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Stage Method Fix Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 STAGE METHOD NAME FIX VERIFIED!")
        print("✅ Uses move_absolute instead of move_to")
        print("✅ Correct parameter order: (y_um, x_um)")
        print("✅ Re-alignment workflow works correctly")
        print("✅ Compatible with StageController interface")
        return True
    else:
        print("\n❌ Some stage method fix tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
