#!/usr/bin/env python3
"""
Test script for the scan folder organization functionality.

This script tests the custom folder prompt and file organization system.
"""

import sys
import os
import tempfile
import shutil
from unittest.mock import Mock, patch

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_folder_validation():
    """Test folder name validation functionality"""
    print("=" * 60)
    print("Testing Folder Name Validation")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_components import MainApp

        # Create Qt application if not exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # Create a mock app instance
        app_instance = MainApp()
        
        # Test valid folder names
        valid_names = [
            "test_scan_001",
            "My Scan Folder",
            "scan-2024-06-30",
            "experiment_batch_1",
            "Test123",
            "a" * 50  # 50 characters
        ]
        
        print("Testing valid folder names:")
        for name in valid_names:
            result = app_instance.validate_folder_name(name)
            if result['valid']:
                print(f"  ✓ '{name}' - Valid")
            else:
                print(f"  ✗ '{name}' - Invalid: {result['error']}")
                return False
        
        # Test invalid folder names
        invalid_names = [
            "",  # Empty
            "   ",  # Only spaces
            "test<file>",  # Invalid characters
            "test/file",  # Invalid characters
            "test\\file",  # Invalid characters
            "test|file",  # Invalid characters
            "CON",  # Reserved name
            "PRN",  # Reserved name
            " test",  # Starts with space
            "test ",  # Ends with space
            ".test",  # Starts with period
            "test.",  # Ends with period
            "a" * 101  # Too long
        ]
        
        print("\nTesting invalid folder names:")
        for name in invalid_names:
            result = app_instance.validate_folder_name(name)
            if not result['valid']:
                print(f"  ✓ '{name}' - Correctly rejected: {result['error']}")
            else:
                print(f"  ✗ '{name}' - Should be invalid but was accepted")
                return False
        
        print("\n✓ Folder validation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Folder validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_creation():
    """Test folder structure creation"""
    print("\n" + "=" * 60)
    print("Testing Folder Structure Creation")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_components import MainApp

        # Create Qt application if not exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temp directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)

            try:
                # Create a mock app instance
                app_instance = MainApp()
                
                # Test folder creation
                test_folder_name = "test_scan_folder"
                folder_path = app_instance.create_scan_folder_structure(test_folder_name)
                
                if folder_path:
                    print(f"✓ Folder created: {folder_path}")
                    
                    # Verify main folder exists
                    if os.path.exists(folder_path):
                        print("✓ Main folder exists")
                    else:
                        print("✗ Main folder does not exist")
                        return False
                    
                    # Verify debug_screenshots subfolder exists
                    debug_folder = os.path.join(folder_path, 'debug_screenshots')
                    if os.path.exists(debug_folder):
                        print("✓ Debug screenshots subfolder exists")
                    else:
                        print("✗ Debug screenshots subfolder does not exist")
                        return False
                    
                    # Test write permissions
                    test_file = os.path.join(folder_path, 'test_write.txt')
                    try:
                        with open(test_file, 'w') as f:
                            f.write("test")
                        os.remove(test_file)
                        print("✓ Write permissions verified")
                    except Exception as e:
                        print(f"✗ Write permission test failed: {str(e)}")
                        return False
                    
                    # Test existing folder handling
                    folder_path2 = app_instance.create_scan_folder_structure(test_folder_name)
                    if folder_path2:
                        print("✓ Existing folder handling works")
                    else:
                        print("✗ Existing folder handling failed")
                        return False
                    
                else:
                    print("✗ Folder creation failed")
                    return False
                
            finally:
                os.chdir(original_cwd)
        
        print("\n✓ Folder creation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Folder creation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_organization():
    """Test file organization within custom folders"""
    print("\n" + "=" * 60)
    print("Testing File Organization")
    print("=" * 60)
    
    try:
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create test folder structure
                test_folder = "test_scan_organization"
                os.makedirs(test_folder)
                os.makedirs(os.path.join(test_folder, 'debug_screenshots'))
                
                # Test CSV file path
                csv_filename = "scan_adaptive_1672531200.csv"
                csv_path = os.path.join(test_folder, csv_filename)
                
                # Create test CSV file
                with open(csv_path, 'w') as f:
                    f.write("step_id,detection_id,center_x,center_y,real_x_um,real_y_um,points,class,pix_origin_x,pix_origin_y\n")
                    f.write("1,1,100,150,50.0,75.0,\"[(90,140),(110,140),(110,160),(90,160)]\",flake,0,0\n")
                
                if os.path.exists(csv_path):
                    print(f"✓ CSV file created: {csv_filename}")
                else:
                    print("✗ CSV file creation failed")
                    return False
                
                # Test reference file path
                reference_filename = "scan_adaptive_1672531200_chip_reference.json"
                reference_path = os.path.join(test_folder, reference_filename)
                
                # Create test reference file
                import json
                test_reference = {
                    "format_version": "2.0",
                    "alignment_method": "chip_boundary",
                    "total_flakes": 1,
                    "creation_timestamp": "2024-06-30 12:00:00"
                }
                
                with open(reference_path, 'w') as f:
                    json.dump(test_reference, f, indent=2)
                
                if os.path.exists(reference_path):
                    print(f"✓ Reference file created: {reference_filename}")
                else:
                    print("✗ Reference file creation failed")
                    return False
                
                # Test log file path
                log_filename = "scan_adaptive_1672531200_log.txt"
                log_path = os.path.join(test_folder, log_filename)
                
                # Create test log file
                with open(log_path, 'w') as f:
                    f.write("=== SCANNING OPERATION LOG ===\n")
                    f.write("Mode: adaptive\n")
                    f.write("Scan Folder: test_scan_organization\n")
                
                if os.path.exists(log_path):
                    print(f"✓ Log file created: {log_filename}")
                else:
                    print("✗ Log file creation failed")
                    return False
                
                # Test debug screenshot path
                debug_screenshot = os.path.join(test_folder, 'debug_screenshots', 'corner_reference_1672531200.png')
                
                # Create test screenshot (empty file for testing)
                with open(debug_screenshot, 'wb') as f:
                    f.write(b'\x89PNG\r\n\x1a\n')  # PNG header
                
                if os.path.exists(debug_screenshot):
                    print(f"✓ Debug screenshot created: debug_screenshots/{os.path.basename(debug_screenshot)}")
                else:
                    print("✗ Debug screenshot creation failed")
                    return False
                
                # Verify folder structure
                expected_files = [csv_filename, reference_filename, log_filename]
                actual_files = [f for f in os.listdir(test_folder) if os.path.isfile(os.path.join(test_folder, f))]
                
                print(f"\nFolder contents verification:")
                print(f"  Expected files: {len(expected_files)}")
                print(f"  Actual files: {len(actual_files)}")
                
                for expected_file in expected_files:
                    if expected_file in actual_files:
                        print(f"  ✓ {expected_file}")
                    else:
                        print(f"  ✗ {expected_file} - Missing")
                        return False
                
                # Verify debug_screenshots subfolder
                debug_files = os.listdir(os.path.join(test_folder, 'debug_screenshots'))
                if debug_files:
                    print(f"  ✓ debug_screenshots/ contains {len(debug_files)} files")
                else:
                    print("  ✗ debug_screenshots/ is empty")
                    return False
                
            finally:
                os.chdir(original_cwd)
        
        print("\n✓ File organization test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ File organization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Scan Folder Organization System")
    print("=" * 80)
    
    tests = [
        ("Folder Name Validation", test_folder_validation),
        ("Folder Structure Creation", test_folder_creation),
        ("File Organization", test_file_organization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} Test PASSED")
        else:
            print(f"❌ {test_name} Test FAILED")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scan folder organization system is working correctly.")
        print("\nKey Features Verified:")
        print("✓ Folder name validation with comprehensive error checking")
        print("✓ Automatic folder structure creation (main + debug_screenshots)")
        print("✓ Proper file organization within custom folders")
        print("✓ Write permission verification")
        print("✓ Existing folder handling")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
