================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:51:12
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751251872.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751251088.json
================================================================================

[2025-06-30 10:51:12.331] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:51:12.343] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:51:12.352] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:51:12.455] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:51:12.466] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:51:12.479] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:51:12.491] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:51:12.502] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:51:12.516] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:51:12.527] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:51:12.665] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:51:12.676] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:51:12.686] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:51:12.699] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:51:12.745] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:51:14.296] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-06-30 10:51:16.296] [INFO] [POSITION] Position feedback: (355.32, 0.00) μm
[2025-06-30 10:51:18.127] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:51:18.265] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 10:51:18.477] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:51:19.764] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:51:19.945] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 10:51:20.456] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-06-30 10:51:20.466] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 691.18) μm
[2025-06-30 10:51:20.476] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:51:20.485] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:51:20.493] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 691.18) μm
[2025-06-30 10:51:20.503] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:51:20.511] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:51:20.520] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:51:20.528] [INFO] [STATUS] Translation: (0.00, 691.18) μm
[2025-06-30 10:51:20.537] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:51:20.545] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:51:20.554] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:51:20.563] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:51:20.572] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:51:20.593] [INFO] [SUCCESS] Successfully transformed 9 flakes
