================================================================================
SCANNING OPERATION LOG - 2025-06-30 13:47:57
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751262474\scan_adaptive_1751262477.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751262474
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751262474\debug_screenshots
================================================================================

[2025-06-30 13:47:57.580] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 13:47:57.593] [INFO] [SYSTEM] Using custom scan folder: scan_1751262474
[2025-06-30 13:47:57.604] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 13:47:57.762] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 13:47:57.773] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 13:47:57.882] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 13:47:58.241] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 13:47:59.101] [INFO] [POSITION] Position feedback: (349.18, 0.00) μm
[2025-06-30 13:47:59.961] [INFO] [POSITION] Position feedback: (694.98, 0.00) μm
[2025-06-30 13:48:00.831] [INFO] [POSITION] Position feedback: (1040.57, 0.00) μm
[2025-06-30 13:48:01.721] [INFO] [POSITION] Position feedback: (1391.87, 0.00) μm
[2025-06-30 13:48:02.571] [INFO] [POSITION] Position feedback: (1731.74, 0.00) μm
[2025-06-30 13:48:03.451] [INFO] [POSITION] Position feedback: (2082.83, 0.00) μm
[2025-06-30 13:48:04.301] [INFO] [POSITION] Position feedback: (2428.20, 0.00) μm
[2025-06-30 13:48:05.211] [INFO] [POSITION] Position feedback: (2774.00, 0.00) μm
[2025-06-30 13:48:06.061] [INFO] [POSITION] Position feedback: (3114.08, 0.00) μm
[2025-06-30 13:48:06.911] [INFO] [POSITION] Position feedback: (3459.67, 0.00) μm
[2025-06-30 13:48:07.771] [INFO] [POSITION] Position feedback: (3805.25, 0.00) μm
[2025-06-30 13:48:08.650] [INFO] [POSITION] Position feedback: (4156.34, 0.00) μm
[2025-06-30 13:48:09.541] [INFO] [POSITION] Position feedback: (4509.55, 0.00) μm
[2025-06-30 13:48:10.400] [INFO] [POSITION] Position feedback: (4842.01, 0.00) μm
[2025-06-30 13:48:11.130] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 13:48:11.291] [INFO] [POSITION] Position feedback: (5183.36, 0.00) μm
[2025-06-30 13:48:11.503] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 13:48:11.725] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 13:48:11.891] [INFO] [POSITION] Position feedback: (5183.79, 0.00) μm
[2025-06-30 13:48:12.403] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 5183.9) μm
[2025-06-30 13:48:12.413] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 13:48:12.531] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 13:48:12.542] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 13:48:12.572] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 13:48:12.584] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 13:48:12.913] [INFO] [STATUS] ✗ Validation failed: Low horizontal line confidence (0.20)
[2025-06-30 13:48:12.929] [INFO] [STATUS] ✗ CRITICAL: Edge detection validation failed
[2025-06-30 13:48:12.939] [INFO] [STATUS]   → Detected edges do not meet quality requirements
[2025-06-30 13:48:12.949] [INFO] [STATUS]   → Insufficient line fitting or poor edge quality
[2025-06-30 13:48:12.959] [ERROR] [ERROR] ✗ CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 13:48:12.968] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 13:48:12.985] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 13:48:12.997] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 13:48:13.007] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 13:48:17.615] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 13:48:17.624] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 13:48:17.750] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 13:48:19.104] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 13:48:19.115] [INFO] [STATUS] Row 0: Reached right edge at column 1
[2025-06-30 13:48:19.128] [INFO] [STATUS] Row 0 complete: 1 positions scanned
[2025-06-30 13:48:19.140] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 13:48:19.271] [INFO] [POSITION] Position feedback: (3823.24, -4.02) μm
[2025-06-30 13:48:22.629] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-06-30 13:48:22.643] [INFO] [STATUS] 
=== Scan Complete ===
[2025-06-30 13:48:22.654] [INFO] [STATUS] Total positions: 1
[2025-06-30 13:48:22.664] [INFO] [STATUS] Total rows: 1
[2025-06-30 13:48:22.679] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751262474\scan_adaptive_1751262477.csv
[2025-06-30 13:48:22.689] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-06-30 13:48:22.700] [INFO] [STATUS] Scanning for annotated images...
[2025-06-30 13:48:22.712] [INFO] [STATUS] Found 99 annotated images
[2025-06-30 13:48:22.733] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No valid step images found
