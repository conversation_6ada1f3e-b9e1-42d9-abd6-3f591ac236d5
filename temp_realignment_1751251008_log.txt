================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:36:48
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751251008.csv
Grid Steps: 0 x 0
Edge Method: background
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751243640.json
================================================================================

[2025-06-30 10:36:48.642] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:36:48.656] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:36:48.665] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:36:48.776] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:36:48.787] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:36:48.798] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:36:48.808] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:36:48.817] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:36:48.829] [INFO] [STATUS] [ChipAlign] Using BackgroundEdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:36:48.841] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:36:48.996] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:36:49.006] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:36:49.015] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:36:49.025] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:36:49.065] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:36:50.506] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 10:36:52.396] [INFO] [POSITION] Position feedback: (372.89, 0.00) μm
[2025-06-30 10:36:54.039] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:36:54.196] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 10:36:54.409] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:36:55.593] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:36:55.726] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 10:36:56.238] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-06-30 10:36:56.249] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 691.18) μm
[2025-06-30 10:36:56.262] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:36:56.272] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:36:56.282] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 691.18) μm
[2025-06-30 10:36:56.293] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:36:56.304] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:36:56.313] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:36:56.324] [INFO] [STATUS] Translation: (0.00, 691.18) μm
[2025-06-30 10:36:56.333] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:36:56.344] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:36:56.355] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:36:56.364] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:36:56.373] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:36:56.385] [INFO] [SUCCESS] Successfully transformed 3 flakes
