================================================================================
SCANNING OPERATION LOG - 2025-06-30 13:07:21
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751260038\scan_adaptive_1751260041.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751260038
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751260038\debug_screenshots
================================================================================

[2025-06-30 13:07:21.853] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 13:07:21.862] [INFO] [SYSTEM] Using custom scan folder: scan_1751260038
[2025-06-30 13:07:21.885] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 13:07:21.998] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 13:07:22.007] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 13:07:22.116] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 13:07:22.498] [INFO] [POSITION] Position feedback: (4.44, 0.00) μm
[2025-06-30 13:07:23.377] [INFO] [POSITION] Position feedback: (349.18, 0.00) μm
[2025-06-30 13:07:24.090] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 13:07:24.227] [INFO] [POSITION] Position feedback: (690.75, 0.00) μm
[2025-06-30 13:07:24.439] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 13:07:24.817] [INFO] [POSITION] Position feedback: (691.17, 9.73) μm
[2025-06-30 13:07:25.549] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 13:07:25.698] [INFO] [POSITION] Position feedback: (691.17, 244.85) μm
[2025-06-30 13:07:26.209] [INFO] [WORKFLOW] Starting position (rotation-robust): (245.2, 691.2) μm
[2025-06-30 13:07:26.220] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 13:07:26.327] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 13:07:26.337] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 13:07:26.373] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 13:07:26.383] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 13:07:26.592] [INFO] [SUCCESS] ✓ Edge detection validation passed (edges: 8257, lines: H+V)
[2025-06-30 13:07:26.603] [INFO] [STATUS] Creating chip boundary reference from detected edges...
[2025-06-30 13:07:26.614] [INFO] [STATUS] [ChipAlign] Creating chip boundary reference from edge detection results...
[2025-06-30 13:07:26.687] [WARNING] [WARNING] [ChipAlign] Warning: Could not get stage position, using (0,0): tuple indices must be integers or slices, not str
[2025-06-30 13:07:26.698] [INFO] [SUCCESS] [ChipAlign] ✓ Chip boundary reference created successfully
[2025-06-30 13:07:26.708] [INFO] [SUCCESS] ✓ Automatic chip boundary reference created successfully
[2025-06-30 13:07:26.727] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\scan_1751260038\auto_chip_reference_1751260046.json
[2025-06-30 13:07:26.737] [INFO] [SUCCESS] ✓ MANDATORY chip boundary reference creation completed successfully
[2025-06-30 13:07:26.748] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 13:07:26.760] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 13:07:26.770] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 13:07:30.380] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 13:07:30.391] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 13:07:30.517] [INFO] [POSITION] Position feedback: (-4.23, 0.00) μm
[2025-06-30 13:07:34.825] [INFO] [PROGRESS] Progress: 2/12 (16.7%)
[2025-06-30 13:07:34.835] [INFO] [STATUS] Position (0,1): Found 0 flakes
[2025-06-30 13:07:34.967] [INFO] [POSITION] Position feedback: (-695.40, 0.00) μm
[2025-06-30 13:07:38.484] [INFO] [PROGRESS] Progress: 3/13 (23.1%)
[2025-06-30 13:07:38.495] [INFO] [STATUS] Position (0,2): Found 0 flakes
[2025-06-30 13:07:38.627] [INFO] [POSITION] Position feedback: (-1386.36, 0.00) μm
[2025-06-30 13:07:42.578] [INFO] [PROGRESS] Progress: 4/14 (28.6%)
[2025-06-30 13:07:42.590] [INFO] [STATUS] Position (0,3): Found 0 flakes
[2025-06-30 13:07:42.716] [INFO] [POSITION] Position feedback: (-2077.75, 0.00) μm
[2025-06-30 13:07:45.380] [INFO] [PROGRESS] Progress: 5/15 (33.3%)
[2025-06-30 13:07:45.390] [INFO] [STATUS] Position (0,4): Found 0 flakes
[2025-06-30 13:07:45.517] [INFO] [POSITION] Position feedback: (-2768.92, 0.00) μm
[2025-06-30 13:07:49.037] [INFO] [PROGRESS] Progress: 6/16 (37.5%)
[2025-06-30 13:07:49.047] [INFO] [STATUS] Position (0,5): Found 0 flakes
[2025-06-30 13:07:49.177] [INFO] [POSITION] Position feedback: (-3460.09, 0.00) μm
[2025-06-30 13:07:52.359] [INFO] [PROGRESS] Progress: 7/17 (41.2%)
[2025-06-30 13:07:52.368] [INFO] [STATUS] Position (0,6): Found 0 flakes
[2025-06-30 13:07:52.497] [INFO] [POSITION] Position feedback: (-4151.26, 0.00) μm
[2025-06-30 13:07:54.925] [INFO] [PROGRESS] Progress: 8/18 (44.4%)
[2025-06-30 13:07:54.936] [INFO] [STATUS] Position (0,7): Found 0 flakes
[2025-06-30 13:07:55.056] [INFO] [POSITION] Position feedback: (-4842.43, 0.00) μm
[2025-06-30 13:07:57.453] [INFO] [PROGRESS] Progress: 9/19 (47.4%)
[2025-06-30 13:07:57.463] [INFO] [STATUS] Position (0,8): Found 0 flakes
[2025-06-30 13:07:57.587] [INFO] [POSITION] Position feedback: (-5533.61, 0.00) μm
[2025-06-30 13:08:02.072] [INFO] [PROGRESS] Progress: 10/20 (50.0%)
[2025-06-30 13:08:02.083] [INFO] [STATUS] Position (0,9): Found 0 flakes
[2025-06-30 13:08:02.206] [INFO] [POSITION] Position feedback: (-6230.07, 0.00) μm
[2025-06-30 13:08:04.662] [INFO] [PROGRESS] Progress: 11/21 (52.4%)
[2025-06-30 13:08:04.672] [INFO] [STATUS] Position (0,10): Found 0 flakes
[2025-06-30 13:08:04.796] [INFO] [POSITION] Position feedback: (-6915.74, 0.00) μm
[2025-06-30 13:08:06.133] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 13:08:06.143] [INFO] [STATUS] Row 0: Reached right edge at column 11
[2025-06-30 13:08:06.153] [INFO] [STATUS] Row 0 complete: 11 positions scanned
[2025-06-30 13:08:06.163] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 13:08:06.286] [INFO] [POSITION] Position feedback: (-7593.15, -4.23) μm
[2025-06-30 13:08:09.634] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-06-30 13:08:09.653] [INFO] [STATUS] 
=== Scan Complete ===
[2025-06-30 13:08:09.663] [INFO] [STATUS] Total positions: 11
[2025-06-30 13:08:09.673] [INFO] [STATUS] Total rows: 1
[2025-06-30 13:08:09.684] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751260038\scan_adaptive_1751260041.csv
[2025-06-30 13:08:09.694] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-06-30 13:08:09.704] [INFO] [STATUS] Scanning for annotated images...
[2025-06-30 13:08:09.718] [INFO] [STATUS] Found 22 annotated images
[2025-06-30 13:08:09.731] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No valid step images found
