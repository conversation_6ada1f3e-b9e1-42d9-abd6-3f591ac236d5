#!/usr/bin/env python3
"""
GUI version of the Enhanced Edge Detection Verification Tool

This provides a simple GUI interface for selecting image files and running
the enhanced edge detection verification with visual file selection.
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import sys
import threading
import subprocess
from pathlib import Path

# Import the verification functions
try:
    from verify_edge_detection import (
        load_image_from_file, capture_screenshot, run_verification,
        create_enhanced_overlay_visualization, create_test_image
    )
    from edge_detection import EdgeDetector, BackgroundEdgeDetector, NoEdgeDetector
    print("Successfully imported enhanced edge detection modules!")
except ImportError as e:
    print(f"Error importing enhanced modules: {e}")
    print("Make sure you're running this from the directory with the new modular files.")


class EdgeDetectionGUI:
    """GUI for Enhanced Edge Detection Verification"""

    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Edge Detection Verification Tool")
        self.root.geometry("600x500")

        # Variables
        self.selected_file = tk.StringVar()
        self.output_dir = tk.StringVar(value="enhanced_verification_results")
        self.status_text = tk.StringVar(value="Ready - Select an image source to begin")

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="Enhanced Edge Detection Verification Tool",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Image source selection
        source_frame = ttk.LabelFrame(main_frame, text="Image Source", padding="10")
        source_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Screenshot button
        screenshot_btn = ttk.Button(source_frame, text="📷 Capture Screenshot",
                                   command=self.capture_screenshot,
                                   style="Accent.TButton")
        screenshot_btn.grid(row=0, column=0, padx=(0, 10), pady=5)

        # File selection
        file_btn = ttk.Button(source_frame, text="📁 Select Image File",
                             command=self.select_file)
        file_btn.grid(row=0, column=1, padx=10, pady=5)

        # Test image button
        test_btn = ttk.Button(source_frame, text="🧪 Create Test Image",
                             command=self.create_test_image)
        test_btn.grid(row=0, column=2, padx=(10, 0), pady=5)

        # Selected file display
        file_frame = ttk.Frame(source_frame)
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(file_frame, text="Selected file:").grid(row=0, column=0, sticky=tk.W)
        file_label = ttk.Label(file_frame, textvariable=self.selected_file,
                              foreground="blue", font=("Arial", 9))
        file_label.grid(row=1, column=0, sticky=tk.W)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(settings_frame, text="Output directory:").grid(row=0, column=0, sticky=tk.W)
        output_entry = ttk.Entry(settings_frame, textvariable=self.output_dir, width=40)
        output_entry.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))

        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))

        run_btn = ttk.Button(action_frame, text="🔍 Run Verification",
                            command=self.run_verification,
                            style="Accent.TButton")
        run_btn.grid(row=0, column=0, padx=(0, 10))

        clear_btn = ttk.Button(action_frame, text="🗑️ Clear Results",
                              command=self.clear_results)
        clear_btn.grid(row=0, column=1, padx=10)

        help_btn = ttk.Button(action_frame, text="❓ Help",
                             command=self.show_help)
        help_btn.grid(row=0, column=2, padx=(10, 0))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Status
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Label(status_frame, text="Status:").grid(row=0, column=0, sticky=tk.W)
        status_label = ttk.Label(status_frame, textvariable=self.status_text,
                                foreground="green", font=("Arial", 9))
        status_label.grid(row=1, column=0, sticky=tk.W)

        # Results text area
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.results_text = tk.Text(results_frame, height=12, width=70, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

    def select_file(self):
        """Open file dialog to select an image file"""
        filetypes = [
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.tif"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Select Image File for Edge Detection Testing",
            filetypes=filetypes
        )

        if filename:
            self.selected_file.set(filename)
            self.status_text.set(f"Selected file: {os.path.basename(filename)}")
            self.log_message(f"Selected file: {filename}")

    def capture_screenshot(self):
        """Capture screenshot from camera view"""
        try:
            self.status_text.set("Capturing screenshot...")
            self.selected_file.set("screenshot")
            self.status_text.set("Screenshot captured - ready to run verification")
            self.log_message("Screenshot captured from camera view")
        except Exception as e:
            self.status_text.set(f"Error capturing screenshot: {str(e)}")
            messagebox.showerror("Screenshot Error", f"Failed to capture screenshot:\n{str(e)}")

    def create_test_image(self):
        """Create a test image for verification"""
        try:
            self.status_text.set("Creating test image...")
            test_path = create_test_image()

            if test_path:
                self.selected_file.set(test_path)
                self.status_text.set(f"Test image created: {os.path.basename(test_path)}")
                self.log_message(f"Created test image: {test_path}")
                messagebox.showinfo("Test Image Created",
                                  f"Test image created successfully!\n\n"
                                  f"Location: {test_path}\n"
                                  f"This image contains a simulated chip with flakes for testing.")
            else:
                self.status_text.set("Failed to create test image")
                messagebox.showerror("Test Image Error", "Failed to create test image")

        except Exception as e:
            self.status_text.set(f"Error creating test image: {str(e)}")
            messagebox.showerror("Test Image Error", f"Failed to create test image:\n{str(e)}")

    def run_verification(self):
        """Run the edge detection verification in a separate thread"""
        if not self.selected_file.get():
            messagebox.showwarning("No Image Selected",
                                 "Please select an image file or capture a screenshot first.")
            return

        # Run in separate thread to avoid blocking GUI
        thread = threading.Thread(target=self._run_verification_thread)
        thread.daemon = True
        thread.start()

    def _run_verification_thread(self):
        """Run verification in background thread"""
        try:
            self.progress.start()
            self.status_text.set("Running edge detection verification...")

            # Clear previous results
            self.results_text.delete(1.0, tk.END)

            # Create output directory
            output_dir = self.output_dir.get()
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Get image
            if self.selected_file.get() == "screenshot":
                img = capture_screenshot()
                source_description = "screenshot"
                self.log_message("Using captured screenshot")
            else:
                img = load_image_from_file(self.selected_file.get())
                source_description = f"file: {os.path.basename(self.selected_file.get())}"
                self.log_message(f"Loaded image from file: {self.selected_file.get()}")

            # Run verification
            self.log_message("Running enhanced edge detection verification...")
            results_summary = run_verification(img, source_description, output_dir)

            # Display results
            self.display_results(results_summary, output_dir, source_description)

            self.status_text.set("✓ Verification completed successfully!")

        except Exception as e:
            self.status_text.set(f"✗ Error: {str(e)}")
            self.log_message(f"ERROR: {str(e)}")
            messagebox.showerror("Verification Error", f"Verification failed:\n{str(e)}")
        finally:
            self.progress.stop()

    def display_results(self, results_summary, output_dir, source_description):
        """Display verification results in the text area"""
        self.log_message("\n" + "="*50)
        self.log_message("ENHANCED VERIFICATION COMPLETE!")
        self.log_message("="*50)

        self.log_message(f"\nTested image from: {source_description}")
        self.log_message(f"Results saved in: {output_dir}")

        self.log_message("\nFiles created:")
        self.log_message("- original_image.png: The input image")
        self.log_message("- *_overlay.png: Enhanced overlay visualizations with test points")
        self.log_message("- *_mask.png: Raw detection masks")

        self.log_message("\nResults Summary:")
        for result in results_summary:
            self.log_message(f"- {result['method']}: {result['chip_percentage']:.1f}% chip area, {result['coverage']:.0f}% coverage")

        self.log_message("\nEnhanced Features Visualization:")
        self.log_message("✓ Green overlay = Detected chip area")
        self.log_message("✓ Colored circles = Test points (Green=on chip, Red=off chip)")
        self.log_message("✓ Coverage percentage = How well the method detects chip boundaries")
        self.log_message("✓ Rotation angle = Detected chip orientation (if available)")

        self.log_message("\nRecommendations:")
        best_method = max(results_summary, key=lambda x: x['coverage'])
        self.log_message(f"• Best performing method: {best_method['method']}")
        self.log_message("• For rotated chips >20°, use 'Background Enhanced' method")
        self.log_message("• Check overlay images to visually verify detection accuracy")

        # Open results folder button
        self.log_message(f"\n📁 Results folder: {os.path.abspath(output_dir)}")

    def clear_results(self):
        """Clear the results text area"""
        self.results_text.delete(1.0, tk.END)
        self.status_text.set("Results cleared - ready for new verification")

    def show_help(self):
        """Show help dialog"""
        help_text = """Enhanced Edge Detection Verification Tool

This tool tests enhanced edge detection methods with rotation robustness.

How to use:
1. Select an image source:
   • 📷 Capture Screenshot: Use current camera view
   • 📁 Select Image File: Choose an image file from disk
   • 🧪 Create Test Image: Generate a sample chip image for testing

2. Configure output directory (optional)

3. Click 🔍 Run Verification to start testing

The tool will test three enhanced methods:
• General Enhanced: Multi-scale edge detection
• Background Enhanced: Color-based detection (best for rotated chips)
• No Edge (Debug): Assumes entire image is chip

Results include:
• Overlay images with visual feedback
• Coverage statistics
• Rotation robustness analysis

For best results with rotated chips (>20°), use the Background Enhanced method.
"""
        messagebox.showinfo("Help", help_text)

    def log_message(self, message):
        """Add message to results text area"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()


def main():
    """Main function to run the GUI"""
    root = tk.Tk()

    # Set up modern styling
    style = ttk.Style()
    style.theme_use('clam')  # Use a modern theme

    app = EdgeDetectionGUI(root)

    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()