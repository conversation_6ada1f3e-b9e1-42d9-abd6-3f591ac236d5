================================================================================
SCANNING OPERATION LOG - 2025-06-30 13:05:40
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751259938\scan_adaptive_1751259940.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751259938
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751259938\debug_screenshots
================================================================================

[2025-06-30 13:05:40.627] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 13:05:40.641] [INFO] [SYSTEM] Using custom scan folder: scan_1751259938
[2025-06-30 13:05:40.654] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 13:05:40.761] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 13:05:40.771] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 13:05:41.330] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 13:05:41.731] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 13:05:42.581] [INFO] [POSITION] Position feedback: (349.18, 0.00) μm
[2025-06-30 13:05:43.310] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 13:05:43.491] [INFO] [POSITION] Position feedback: (690.75, 0.00) μm
[2025-06-30 13:05:43.702] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 13:05:44.312] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 13:05:44.471] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 13:05:44.984] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-06-30 13:05:44.994] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 13:05:45.101] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 13:05:45.111] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 13:05:45.153] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 13:05:45.164] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 13:05:45.382] [INFO] [SUCCESS] ✓ Edge detection validation passed (edges: 4548, lines: H+V)
[2025-06-30 13:05:45.393] [INFO] [STATUS] Creating chip boundary reference from detected edges...
[2025-06-30 13:05:45.403] [INFO] [STATUS] [ChipAlign] Creating chip boundary reference from edge detection results...
[2025-06-30 13:05:45.481] [WARNING] [WARNING] [ChipAlign] Warning: Could not get stage position, using (0,0): tuple indices must be integers or slices, not str
[2025-06-30 13:05:45.492] [INFO] [SUCCESS] [ChipAlign] ✓ Chip boundary reference created successfully
[2025-06-30 13:05:45.505] [INFO] [SUCCESS] ✓ Automatic chip boundary reference created successfully
[2025-06-30 13:05:45.525] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\scan_1751259938\auto_chip_reference_1751259945.json
[2025-06-30 13:05:45.535] [INFO] [SUCCESS] ✓ MANDATORY chip boundary reference creation completed successfully
[2025-06-30 13:05:45.546] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 13:05:45.558] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 13:05:45.568] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 13:05:49.493] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 13:05:49.502] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 13:05:49.630] [INFO] [POSITION] Position feedback: (-4.23, 0.00) μm
[2025-06-30 13:05:54.654] [INFO] [PROGRESS] Progress: 2/12 (16.7%)
[2025-06-30 13:05:54.664] [INFO] [STATUS] Position (0,1): Found 0 flakes
