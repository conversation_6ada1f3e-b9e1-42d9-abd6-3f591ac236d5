#!/usr/bin/env python3
"""
Test script to verify proper Object-Oriented Design principles in the refactored edge detector hierarchy.
"""

import sys
import inspect
import numpy as np
import cv2
from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector

def test_base_class_design():
    """Test that the base EdgeDetector class follows proper OOP design principles."""
    print("=== Testing Base Class Design Principles ===")
    
    # Test 1: Base class should NOT have detect_chip_edges_canny method
    print("\n--- Test 1: Base Class Method Separation ---")
    
    if hasattr(EdgeDetector, 'detect_chip_edges_canny'):
        print("❌ VIOLATION: Base EdgeDetector class has detect_chip_edges_canny method")
        print("   This violates Single Responsibility Principle - base class should not implement specific algorithms")
        return False
    else:
        print("✅ CORRECT: Base EdgeDetector class does NOT have detect_chip_edges_canny method")
    
    # Test 2: Only CannyEdgeDetector should have detect_chip_edges_canny
    print("\n--- Test 2: Specific Algorithm Ownership ---")
    
    if hasattr(CannyEdgeDetector, 'detect_chip_edges_canny'):
        print("✅ CORRECT: CannyEdgeDetector has detect_chip_edges_canny method")
    else:
        print("❌ ERROR: CannyEdgeDetector missing detect_chip_edges_canny method")
        return False
    
    if hasattr(BackgroundEdgeDetector, 'detect_chip_edges_canny'):
        print("⚠️  WARNING: BackgroundEdgeDetector has detect_chip_edges_canny method")
        print("   This is acceptable for interface consistency but should use its own detection method")
    
    return True

def test_method_responsibility_separation():
    """Test that each class uses its own edge detection method appropriately."""
    print("\n=== Testing Method Responsibility Separation ===")
    
    # Create test image
    test_img = np.ones((300, 400, 3), dtype=np.uint8) * 200
    cv2.rectangle(test_img, (100, 75), (300, 225), (50, 50, 50), -1)
    
    classes_to_test = [
        ('EdgeDetector', EdgeDetector),
        ('BackgroundEdgeDetector', BackgroundEdgeDetector),
        ('CannyEdgeDetector', CannyEdgeDetector)
    ]
    
    for name, cls in classes_to_test:
        print(f"\n--- Testing {name} Method Usage ---")
        
        try:
            detector = cls(debug=False)
            
            # Test detect_edges_with_line_fitting method
            result = detector.detect_edges_with_line_fitting(test_img, 'sequential')
            
            if result and 'edges' in result:
                print(f"✅ {name} successfully uses its own edge detection method")
            else:
                print(f"❌ {name} failed to produce edge detection results")
                return False
                
        except Exception as e:
            print(f"❌ {name} failed with error: {str(e)}")
            return False
    
    return True

def test_no_special_case_handling():
    """Test that there's no special case handling based on class type."""
    print("\n=== Testing No Special Case Handling ===")
    
    # Check the source code of detect_edges_with_line_fitting in base class
    base_method = EdgeDetector.detect_edges_with_line_fitting
    source = inspect.getsource(base_method)
    
    # Look for problematic patterns
    problematic_patterns = [
        'type(self).__name__',
        'isinstance(self,',
        'hasattr(self, \'detect_chip_edges_canny\') and',
        'if.*CannyEdgeDetector'
    ]
    
    violations = []
    for pattern in problematic_patterns:
        if pattern in source:
            violations.append(pattern)
    
    if violations:
        print("❌ VIOLATION: Base class has special case handling:")
        for violation in violations:
            print(f"   Found: {violation}")
        print("   This violates Open/Closed Principle - base class should not know about subclasses")
        return False
    else:
        print("✅ CORRECT: Base class has no special case handling for subclasses")
    
    return True

def test_inheritance_hierarchy():
    """Test that the inheritance hierarchy follows proper OOP principles."""
    print("\n=== Testing Inheritance Hierarchy ===")
    
    # Test 1: All classes should inherit from EdgeDetector
    print("\n--- Test 1: Inheritance Chain ---")
    
    if not issubclass(BackgroundEdgeDetector, EdgeDetector):
        print("❌ BackgroundEdgeDetector does not inherit from EdgeDetector")
        return False
    
    if not issubclass(CannyEdgeDetector, EdgeDetector):
        print("❌ CannyEdgeDetector does not inherit from EdgeDetector")
        return False
    
    print("✅ All classes properly inherit from EdgeDetector")
    
    # Test 2: Each class should have its own implementation of detect_chip_edges
    print("\n--- Test 2: Method Override Verification ---")
    
    base_method = EdgeDetector.detect_chip_edges
    bg_method = BackgroundEdgeDetector.detect_chip_edges
    canny_method = CannyEdgeDetector.detect_chip_edges
    
    # Check if methods are overridden (different implementations)
    if bg_method == base_method:
        print("❌ BackgroundEdgeDetector does not override detect_chip_edges")
        return False
    
    if canny_method == base_method:
        print("❌ CannyEdgeDetector does not override detect_chip_edges")
        return False
    
    print("✅ All subclasses properly override detect_chip_edges method")
    
    return True

def test_interface_consistency():
    """Test that all classes have consistent interfaces for line fitting."""
    print("\n=== Testing Interface Consistency ===")
    
    required_line_fitting_methods = [
        'fit_lines_hough',
        'fit_lines_ransac',
        'detect_edges_with_line_fitting'
    ]
    
    classes = [EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector]
    
    for method_name in required_line_fitting_methods:
        print(f"\n--- Testing {method_name} consistency ---")
        
        signatures = []
        for cls in classes:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                sig = inspect.signature(method)
                signatures.append((cls.__name__, str(sig)))
                print(f"  {cls.__name__}: {sig}")
            else:
                print(f"❌ {cls.__name__} missing {method_name}")
                return False
        
        # Check if all signatures are identical
        if len(set(sig[1] for sig in signatures)) == 1:
            print(f"✅ All classes have identical {method_name} signatures")
        else:
            print(f"❌ {method_name} signatures differ between classes")
            return False
    
    return True

def main():
    """Run all OOP design principle tests."""
    print("Testing Object-Oriented Design Principles")
    print("=" * 60)
    
    tests = [
        ("Base Class Design", test_base_class_design),
        ("Method Responsibility", test_method_responsibility_separation),
        ("No Special Case Handling", test_no_special_case_handling),
        ("Inheritance Hierarchy", test_inheritance_hierarchy),
        ("Interface Consistency", test_interface_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"\n✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            print(f"\n💥 {test_name} CRASHED: {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"OOP Design Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"\n🎉 EXCELLENT OOP DESIGN!")
        print(f"✅ Single Responsibility Principle: Each class has its own edge detection method")
        print(f"✅ Open/Closed Principle: No special case handling in base class")
        print(f"✅ Liskov Substitution Principle: All classes can be used interchangeably")
        print(f"✅ Interface Segregation: Consistent line-fitting interfaces")
        print(f"✅ Dependency Inversion: Base class defines interface, subclasses implement")
        return True
    else:
        print(f"\n❌ OOP design violations detected")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
