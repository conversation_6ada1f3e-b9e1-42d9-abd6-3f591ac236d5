================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:53:47
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751252027.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751251088.json
================================================================================

[2025-06-30 10:53:47.685] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:53:47.695] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:53:47.704] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:53:47.830] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:53:47.840] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:53:47.852] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:53:47.862] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:53:47.874] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:53:47.883] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:53:47.893] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:53:48.050] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:53:48.061] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:53:48.071] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:53:48.080] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:53:48.127] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:53:49.650] [INFO] [POSITION] Position feedback: (27.51, 0.00) μm
[2025-06-30 10:53:51.710] [INFO] [POSITION] Position feedback: (355.53, 0.00) μm
[2025-06-30 10:53:53.770] [INFO] [POSITION] Position feedback: (708.53, 0.00) μm
[2025-06-30 10:53:55.547] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:53:55.710] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-06-30 10:53:55.919] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:53:57.560] [INFO] [POSITION] Position feedback: (1036.76, 4.02) μm
[2025-06-30 10:53:59.540] [INFO] [POSITION] Position feedback: (1036.76, 272.36) μm
[2025-06-30 10:54:01.448] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:54:01.590] [INFO] [POSITION] Position feedback: (1036.76, 490.34) μm
[2025-06-30 10:54:02.103] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (490.4, 1036.8) μm
[2025-06-30 10:54:02.115] [INFO] [STATUS] [ChipAlign] Current chip origin: (490.37, 1036.77) μm
[2025-06-30 10:54:02.125] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:54:02.136] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:54:02.147] [INFO] [STATUS] [ChipAlign] Translation: (490.37, 1036.77) μm
[2025-06-30 10:54:02.158] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:54:02.167] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:54:02.178] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:54:02.187] [INFO] [STATUS] Translation: (490.37, 1036.77) μm
[2025-06-30 10:54:02.197] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:54:02.208] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:54:02.217] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:54:02.227] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:54:02.237] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:54:02.280] [INFO] [SUCCESS] Successfully transformed 9 flakes
