#!/usr/bin/env python3
"""
Test script to verify that the scanning workflow uses consistent edge detection
between reference creation and scanning operations.

This test verifies the fix for the architectural inconsistency where
_perform_edge_detection_with_capabilities() was using different detectors
for different edge detector types.
"""

import numpy as np
import cv2
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector, NoEdgeDetector


def create_test_image():
    """Create a simple test image for edge detection testing"""
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    
    # Create a chip-like rectangle
    cv2.rectangle(img, (100, 50), (500, 350), (180, 180, 180), -1)  # Gray chip
    cv2.rectangle(img, (0, 0), (600, 400), (200, 150, 100), 10)     # Beige border
    
    return img


def test_detector_consistency():
    """Test that all detectors have consistent detect_edges_with_line_fitting interface"""
    print("Testing Edge Detection Consistency")
    print("=" * 50)
    
    # Create test image
    img = create_test_image()
    print(f"Created test image with shape: {img.shape}")
    
    # Test all detector types
    detectors = [
        ('EdgeDetector', EdgeDetector(debug=False)),
        ('BackgroundEdgeDetector', BackgroundEdgeDetector(debug=False)),
        ('CannyEdgeDetector', CannyEdgeDetector(debug=False)),
        ('NoEdgeDetector', NoEdgeDetector(debug=False))
    ]
    
    results = {}
    
    for detector_name, detector in detectors:
        print(f"\nTesting {detector_name}...")
        
        try:
            # Test the unified interface
            result = detector.detect_edges_with_line_fitting(
                img,
                algorithm_mode='sequential'
            )
            
            if result is None:
                print(f"  ✗ {detector_name}: Returned None")
                results[detector_name] = {'success': False, 'error': 'Returned None'}
                continue
            
            # Check required fields
            required_fields = ['edges', 'fitted_lines', 'algorithm_mode']
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                print(f"  ✗ {detector_name}: Missing fields: {missing_fields}")
                results[detector_name] = {'success': False, 'error': f'Missing fields: {missing_fields}'}
                continue
            
            # Check edge detection results
            edges = result['edges']
            if edges is None:
                print(f"  ✗ {detector_name}: No edges detected")
                results[detector_name] = {'success': False, 'error': 'No edges detected'}
                continue
            
            edge_pixels = np.sum(edges > 0)
            total_pixels = edges.size
            edge_percentage = (edge_pixels / total_pixels) * 100
            
            print(f"  ✓ {detector_name}: Success")
            print(f"    - Edge pixels: {edge_percentage:.2f}% of image")
            print(f"    - Algorithm mode: {result['algorithm_mode']}")
            print(f"    - Fitted lines: {result['fitted_lines'] is not None}")
            
            results[detector_name] = {
                'success': True,
                'edge_percentage': edge_percentage,
                'algorithm_mode': result['algorithm_mode'],
                'has_fitted_lines': result['fitted_lines'] is not None
            }
            
        except Exception as e:
            print(f"  ✗ {detector_name}: Exception: {str(e)}")
            results[detector_name] = {'success': False, 'error': str(e)}
    
    return results


def test_scanning_workflow_simulation():
    """Simulate the scanning workflow to test consistency"""
    print("\n" + "=" * 50)
    print("Testing Scanning Workflow Simulation")
    print("=" * 50)
    
    img = create_test_image()
    
    # Simulate the fixed _perform_edge_detection_with_capabilities method
    def simulate_perform_edge_detection_with_capabilities(edge_detector, img):
        """Simulate the fixed method from scanning.py"""
        try:
            detector_type = type(edge_detector).__name__
            print(f"Using {detector_type} with unified line fitting interface...")

            # All detectors now have the same detect_edges_with_line_fitting() interface
            edge_result = edge_detector.detect_edges_with_line_fitting(
                img,
                algorithm_mode='sequential'
            )

            if edge_result is None:
                print(f"✗ {detector_type} returned no results")
                return None

            # Validate required fields
            required_fields = ['edges', 'fitted_lines', 'algorithm_mode']
            missing_fields = [field for field in required_fields if field not in edge_result]
            if missing_fields:
                print(f"✗ {detector_type} result missing fields: {missing_fields}")
                return None

            print(f"✓ {detector_type} edge detection completed successfully")
            return edge_result

        except Exception as e:
            print(f"✗ Error in {detector_type} edge detection: {str(e)}")
            return None
    
    # Test with different detector types
    test_detectors = [
        BackgroundEdgeDetector(debug=False),
        CannyEdgeDetector(debug=False),
        EdgeDetector(debug=False)
    ]
    
    for detector in test_detectors:
        detector_name = type(detector).__name__
        print(f"\nSimulating scanning workflow with {detector_name}...")
        
        result = simulate_perform_edge_detection_with_capabilities(detector, img)
        
        if result:
            print(f"  ✓ {detector_name} workflow simulation successful")
            print(f"    - Uses same detector for reference creation and scanning")
            print(f"    - Algorithm mode: {result['algorithm_mode']}")
        else:
            print(f"  ✗ {detector_name} workflow simulation failed")


def main():
    """Main test function"""
    print("Edge Detection Consistency Test")
    print("Testing the fix for scanning workflow architectural inconsistency")
    print("=" * 70)
    
    # Test 1: Detector consistency
    detector_results = test_detector_consistency()
    
    # Test 2: Scanning workflow simulation
    test_scanning_workflow_simulation()
    
    # Summary
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    
    successful_detectors = [name for name, result in detector_results.items() if result.get('success', False)]
    failed_detectors = [name for name, result in detector_results.items() if not result.get('success', False)]
    
    print(f"✓ Successful detectors: {len(successful_detectors)}")
    for detector in successful_detectors:
        print(f"  - {detector}")
    
    if failed_detectors:
        print(f"✗ Failed detectors: {len(failed_detectors)}")
        for detector in failed_detectors:
            error = detector_results[detector].get('error', 'Unknown error')
            print(f"  - {detector}: {error}")
    
    print(f"\nArchitectural Consistency: {'✓ FIXED' if len(successful_detectors) >= 3 else '✗ ISSUES REMAIN'}")
    print("All detectors now use the same unified interface in scanning workflow.")


if __name__ == "__main__":
    main()
