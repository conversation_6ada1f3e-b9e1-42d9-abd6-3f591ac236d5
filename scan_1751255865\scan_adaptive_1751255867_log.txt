================================================================================
SCANNING OPERATION LOG - 2025-06-30 11:57:47
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751255865\scan_adaptive_1751255867.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751255865
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751255865\debug_screenshots
================================================================================

[2025-06-30 11:57:47.264] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 11:57:47.277] [INFO] [SYSTEM] Using custom scan folder: scan_1751255865
[2025-06-30 11:57:47.291] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 11:57:47.404] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 11:57:47.414] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 11:57:47.528] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 11:57:47.934] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 11:57:48.690] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 11:57:48.834] [INFO] [POSITION] Position feedback: (345.37, 0.00) μm
[2025-06-30 11:57:49.045] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 11:57:49.413] [INFO] [POSITION] Position feedback: (345.59, 4.02) μm
[2025-06-30 11:57:50.160] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 11:57:50.314] [INFO] [POSITION] Position feedback: (345.59, 244.85) μm
[2025-06-30 11:57:50.826] [INFO] [WORKFLOW] Starting position (rotation-robust): (245.2, 345.6) μm
[2025-06-30 11:57:50.838] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 11:57:50.933] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 11:57:50.944] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 11:57:50.990] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 11:57:51.001] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 11:57:51.239] [INFO] [STATUS] ✗ Validation failed: Missing line orientations (H:False, V:True)
[2025-06-30 11:57:51.250] [INFO] [STATUS] ✗ CRITICAL: Edge detection validation failed
[2025-06-30 11:57:51.261] [INFO] [STATUS]   → Detected edges do not meet quality requirements
[2025-06-30 11:57:51.271] [INFO] [STATUS]   → Insufficient line fitting or poor edge quality
[2025-06-30 11:57:51.283] [ERROR] [ERROR] ✗ CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 11:57:51.294] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 11:57:51.307] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 11:57:51.318] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 11:57:51.328] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 11:57:55.271] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 11:57:55.280] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 11:57:55.404] [INFO] [POSITION] Position feedback: (-9.52, 4.23) μm
[2025-06-30 11:57:56.813] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 11:57:56.827] [INFO] [STATUS] Row 0: Reached right edge at column 1
[2025-06-30 11:57:56.838] [INFO] [STATUS] Row 0 complete: 1 positions scanned
[2025-06-30 11:57:56.849] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 11:57:56.973] [INFO] [POSITION] Position feedback: (-335.85, 240.83) μm
[2025-06-30 11:58:00.366] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-06-30 11:58:00.379] [INFO] [STATUS] 
=== Scan Complete ===
[2025-06-30 11:58:00.389] [INFO] [STATUS] Total positions: 1
[2025-06-30 11:58:00.400] [INFO] [STATUS] Total rows: 1
[2025-06-30 11:58:00.412] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751255865\scan_adaptive_1751255867.csv
[2025-06-30 11:58:00.422] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-06-30 11:58:00.433] [INFO] [STATUS] Scanning for annotated images...
[2025-06-30 11:58:00.445] [INFO] [STATUS] Found 1 annotated images
[2025-06-30 11:58:00.463] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No valid step images found
