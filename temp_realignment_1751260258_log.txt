================================================================================
SCANNING OPERATION LOG - 2025-06-30 13:10:58
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751260258.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/scan_1751260038/auto_chip_reference_1751260046.json
================================================================================

[2025-06-30 13:10:58.162] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 13:10:58.173] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 13:10:58.185] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 13:10:58.310] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 13:10:58.320] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 13:10:58.332] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 13:10:58.342] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 13:10:58.357] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 13:10:58.368] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 13:10:58.378] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 13:10:58.540] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 13:10:58.557] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 13:10:58.568] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 13:10:58.579] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 13:10:58.618] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 13:11:00.350] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-06-30 13:11:02.640] [INFO] [POSITION] Position feedback: (200.41, 0.00) μm
[2025-06-30 13:11:04.860] [INFO] [POSITION] Position feedback: (355.11, 0.00) μm
[2025-06-30 13:11:06.929] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 13:11:07.099] [INFO] [POSITION] Position feedback: (518.27, 0.00) μm
[2025-06-30 13:11:07.319] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 13:11:09.179] [INFO] [POSITION] Position feedback: (518.27, 9.31) μm
[2025-06-30 13:11:11.499] [INFO] [POSITION] Position feedback: (518.27, 139.89) μm
[2025-06-30 13:11:13.631] [INFO] [POSITION] Position feedback: (518.27, 249.08) μm
[2025-06-30 13:11:15.680] [INFO] [POSITION] Position feedback: (518.27, 371.83) μm
[2025-06-30 13:11:17.652] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 13:11:17.819] [INFO] [POSITION] Position feedback: (518.27, 490.34) μm
[2025-06-30 13:11:18.330] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (490.4, 518.4) μm
[2025-06-30 13:11:18.765] [INFO] [STATUS] [ChipAlign] Corner screenshot saved: debug_screenshots\corner_realignment_1751260278.png
[2025-06-30 13:11:18.775] [INFO] [STATUS] [ChipAlign] Corner position: (490.37, 518.38) μm
[2025-06-30 13:11:18.790] [INFO] [STATUS] [ChipAlign] Current chip origin: (490.37, 518.38) μm
[2025-06-30 13:11:18.800] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 13:11:18.810] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 13:11:18.819] [INFO] [STATUS] [ChipAlign] Translation: (490.37, 518.38) μm
[2025-06-30 13:11:18.829] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 13:11:18.839] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 13:11:18.856] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 13:11:18.867] [INFO] [STATUS] Translation: (490.37, 518.38) μm
[2025-06-30 13:11:18.879] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 13:11:18.891] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 13:11:18.904] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 13:11:18.916] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 13:11:18.927] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/scan_1751260038/scan_adaptive_1751260041.csv
[2025-06-30 13:11:18.942] [INFO] [SUCCESS] Successfully transformed 0 flakes
