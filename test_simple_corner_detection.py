#!/usr/bin/env python3
"""
Simple test to verify that the corner detection method works correctly.
"""

import sys
from unittest.mock import Mock

# Import the functions
from chip_alignment import ChipAlignmentSystem
from edge_detection import BackgroundEdgeDetector

def test_corner_detection_basic():
    """Basic test of corner detection functionality."""
    print("=== Testing Basic Corner Detection ===")
    
    try:
        # Create mock stage controller
        mock_stage = Mock()
        mock_stage.set_zero = Mock()
        mock_stage.move_absolute = Mock()
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        # Create edge detector
        edge_detector = BackgroundEdgeDetector(debug=False)
        
        # Mock is_on_chip to find edges quickly
        call_count = [0]
        def mock_is_on_chip(img, x, y, margin=20):
            call_count[0] += 1
            # Find edge after 2 calls
            return call_count[0] < 2
        
        edge_detector.is_on_chip = Mock(side_effect=mock_is_on_chip)
        
        print("\n--- Testing Corner Detection ---")
        
        # Test corner detection
        corner_coords = alignment_system._find_current_upper_left_corner(edge_detector)
        
        # Verify the results
        print(f"\n--- Verifying Results ---")
        
        if corner_coords is not None:
            print(f"✅ Corner detection succeeded: {corner_coords}")
            
            # Verify that move_absolute was called
            if mock_stage.move_absolute.call_count > 0:
                print(f"✅ Stage movements executed: {mock_stage.move_absolute.call_count} calls")
            else:
                print(f"❌ No stage movements detected")
                return False
            
            # Verify the final coordinates are reasonable
            final_x, final_y = corner_coords
            if isinstance(final_x, (int, float)) and isinstance(final_y, (int, float)):
                print(f"✅ Final coordinates are valid: x={final_x}, y={final_y}")
            else:
                print(f"❌ Final coordinates are invalid: x={final_x}, y={final_y}")
                return False
            
        else:
            print(f"❌ Corner detection failed (returned None)")
            return False
        
        print(f"\n🎉 Basic corner detection works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_realignment_integration():
    """Test that re-alignment integrates correctly with corner detection."""
    print(f"\n=== Testing Re-alignment Integration ===")
    
    try:
        # Create mock stage
        mock_stage = Mock()
        mock_stage.set_zero = Mock()
        mock_stage.move_absolute = Mock()
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        # Mock the corner detection to return a specific result
        def mock_find_corner(edge_detector):
            print(f"  Corner detection executed successfully")
            return (105.0, 155.0)  # Return found corner
        
        alignment_system._find_current_upper_left_corner = mock_find_corner
        
        # Create test reference data
        reference_data = {
            'boundary_reference': {'chip_origin_abs': [100.0, 150.0]},
            'relative_flakes': [
                {'id': 1, 'center_x': 50, 'center_y': 75, 'real_x_um_rel': 50.0, 'real_y_um_rel': 75.0}
            ],
            'edge_detection_method': 'BackgroundEdgeDetector'
        }
        
        print("\n--- Testing Re-alignment Workflow ---")
        
        # Perform re-alignment
        result = alignment_system.perform_realignment(reference_data)
        
        if result.get('success'):
            print(f"✅ Re-alignment completed successfully")
            print(f"  Translation: {result.get('transformation', {}).get('translation', 'N/A')}")
            print(f"  Transformed flakes: {len(result.get('transformed_flakes', []))}")
            
            # Verify the transformation is correct
            expected_translation = (5.0, 5.0)  # 105-100, 155-150
            actual_translation = result.get('transformation', {}).get('translation')
            if actual_translation == expected_translation:
                print(f"✅ Transformation calculation is correct: {actual_translation}")
            else:
                print(f"❌ Wrong transformation: expected {expected_translation}, got {actual_translation}")
                return False
            
        else:
            error_msg = result.get('error', 'Unknown error')
            print(f"❌ Re-alignment failed: {error_msg}")
            return False
        
        print(f"\n🎉 Re-alignment integration works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        return False

def main():
    """Run simple corner detection tests."""
    print("Testing Simple Corner Detection")
    print("=" * 60)
    
    tests = [
        test_corner_detection_basic,
        test_realignment_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            print(f"✗ {test.__name__} CRASHED: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Simple Corner Detection Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 CORNER DETECTION IS WORKING CORRECTLY!")
        print("✅ Basic corner detection functionality works")
        print("✅ Re-alignment integration is successful")
        print("✅ Transformation calculation is accurate")
        print("✅ Ready for real-world testing")
        return True
    else:
        print("\n❌ Some corner detection tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
