#!/usr/bin/env python3
"""
Test script for the complete chip boundary re-alignment workflow.

This script tests the end-to-end workflow:
1. Initial scan → reference creation
2. Re-alignment → CSV transformation 
3. Flake selector display

Usage:
    python test_chip_boundary_workflow.py
"""

import sys
import os
import tempfile
import json
import csv
import numpy as np
from unittest.mock import Mock, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_csv_transformation():
    """Test CSV transformation functionality"""
    print("=" * 60)
    print("Testing CSV Transformation Functionality")
    print("=" * 60)
    
    try:
        from scanning import ScanWorker
        
        # Create a mock ScanWorker
        worker = <PERSON>anWorker(
            mode='grid',
            x_steps=5,
            y_steps=5,
            out_csv='test.csv',
            region=(100, 100, 800, 600),
            enable_chip_alignment=True
        )
        
        # Create test CSV data
        test_csv_file = 'test_scan_data.csv'
        test_flakes = [
            {
                'step_id': '1',
                'detection_id': '1',
                'center_x': 100,
                'center_y': 150,
                'real_x_um': 50.0,
                'real_y_um': 75.0,
                'points': '[(90, 140), (110, 140), (110, 160), (90, 160)]',
                'class': 'flake',
                'pix_origin_x': 0,
                'pix_origin_y': 0
            },
            {
                'step_id': '2',
                'detection_id': '1',
                'center_x': 200,
                'center_y': 250,
                'real_x_um': 100.0,
                'real_y_um': 125.0,
                'points': '[(190, 240), (210, 240), (210, 260), (190, 260)]',
                'class': 'flake',
                'pix_origin_x': 0,
                'pix_origin_y': 0
            }
        ]
        
        # Write test CSV file
        with open(test_csv_file, 'w', newline='') as f:
            fieldnames = ['step_id', 'detection_id', 'center_x', 'center_y', 
                         'real_x_um', 'real_y_um', 'points', 'class', 
                         'pix_origin_x', 'pix_origin_y']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(test_flakes)
        
        print(f"✓ Created test CSV file: {test_csv_file}")
        
        # Test transformation
        transformation = {
            'translation': [10.0, 20.0],  # 10 μm right, 20 μm up
            'rotation_degrees': 0.0,
            'rotation_radians': 0.0
        }
        
        print("Testing CSV transformation...")
        result = worker.load_csv_and_apply_transformation(test_csv_file, transformation)
        
        if result['success']:
            print("✓ CSV transformation successful!")
            print(f"  Transformed {len(result['transformed_flakes'])} flakes")
            
            # Check transformation results
            for i, flake in enumerate(result['transformed_flakes']):
                orig_x = test_flakes[i]['real_x_um']
                orig_y = test_flakes[i]['real_y_um']
                trans_x = flake['real_x_um_transformed']
                trans_y = flake['real_y_um_transformed']
                
                expected_x = orig_x + transformation['translation'][0]
                expected_y = orig_y + transformation['translation'][1]
                
                print(f"  Flake {i+1}: ({orig_x}, {orig_y}) → ({trans_x:.1f}, {trans_y:.1f})")
                print(f"    Expected: ({expected_x:.1f}, {expected_y:.1f})")
                
                if abs(trans_x - expected_x) < 0.1 and abs(trans_y - expected_y) < 0.1:
                    print("    ✓ Transformation correct")
                else:
                    print("    ✗ Transformation incorrect")
                    return False
            
            # Test saving transformed data
            output_file = 'test_transformed.csv'
            worker._save_aligned_flakes(result['transformed_flakes'], output_file)
            
            if os.path.exists(output_file):
                print(f"✓ Transformed data saved to: {output_file}")
                
                # Verify saved file
                with open(output_file, 'r') as f:
                    reader = csv.DictReader(f)
                    saved_flakes = list(reader)
                    print(f"✓ Verified saved file contains {len(saved_flakes)} flakes")
                
                # Clean up
                os.remove(output_file)
            else:
                print("✗ Failed to save transformed data")
                return False
        else:
            print(f"✗ CSV transformation failed: {result['error']}")
            return False
        
        # Clean up
        os.remove(test_csv_file)
        
        print("✓ CSV transformation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ CSV transformation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_reference_loading():
    """Test reference file loading functionality"""
    print("\n" + "=" * 60)
    print("Testing Reference File Loading")
    print("=" * 60)
    
    try:
        from chip_alignment import save_chip_reference, load_chip_reference
        
        # Create test reference data
        test_reference_data = {
            'format_version': '2.0',
            'alignment_method': 'chip_boundary',
            'creation_timestamp': '2025-06-30 12:00:00',
            'chip_origin_abs': [100.0, 150.0],
            'boundary_reference': {
                'chip_origin_abs': [100.0, 150.0],
                'left_edge_angle': 90.0,
                'top_edge_angle': 0.0,
                'confidence_score': 0.95,
                'detection_timestamp': '2025-06-30 12:00:00',
                'chip_boundary_detection_method': 'Canny+Hough+RANSAC'
            },
            'relative_flakes': [],
            'total_flakes': 0,
            'edge_detection_method': 'CannyEdgeDetector',
            'metadata': {
                'creation_mode': 'test'
            }
        }
        
        # Test saving reference
        test_ref_file = 'test_reference.json'
        save_result = save_chip_reference(test_reference_data, test_ref_file)
        
        if save_result['success']:
            print(f"✓ Reference file saved: {test_ref_file}")
        else:
            print(f"✗ Failed to save reference: {save_result['error']}")
            return False
        
        # Test loading reference
        load_result = load_chip_reference(test_ref_file)
        
        if load_result['success']:
            print("✓ Reference file loaded successfully")
            loaded_data = load_result['reference_data']
            
            # Verify data integrity
            if loaded_data['alignment_method'] == 'chip_boundary':
                print("✓ Alignment method correct")
            else:
                print("✗ Alignment method incorrect")
                return False
                
            if loaded_data['edge_detection_method'] == 'CannyEdgeDetector':
                print("✓ Edge detection method correct")
            else:
                print("✗ Edge detection method incorrect")
                return False
                
        else:
            print(f"✗ Failed to load reference: {load_result['error']}")
            return False
        
        # Clean up
        os.remove(test_ref_file)
        
        print("✓ Reference file loading test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Reference loading test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Complete Chip Boundary Re-alignment Workflow")
    print("=" * 80)
    
    tests = [
        ("CSV Transformation", test_csv_transformation),
        ("Reference Loading", test_reference_loading),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} Test PASSED")
        else:
            print(f"❌ {test_name} Test FAILED")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The chip boundary re-alignment workflow is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
