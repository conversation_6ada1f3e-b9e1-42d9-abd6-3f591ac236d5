#!/usr/bin/env python3
"""
Test script for the Chip Alignment Debug Panel

This script tests the visual comparison panel functionality.
"""

import sys
import os
import numpy as np
import cv2

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_corner_images():
    """Create test corner images for debugging"""
    print("Creating test corner images...")
    
    # Create debug directory
    debug_dir = "debug_screenshots"
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
    
    # Create a test "original" corner image
    original_img = np.ones((600, 800, 3), dtype=np.uint8) * 128  # Gray background
    
    # Draw chip edges (original position)
    cv2.line(original_img, (100, 0), (100, 600), (0, 0, 0), 5)  # Left edge (vertical)
    cv2.line(original_img, (0, 150), (800, 150), (0, 0, 0), 5)  # Top edge (horizontal)
    
    # Add some noise/texture
    noise = np.random.randint(-30, 30, original_img.shape, dtype=np.int16)
    original_img = np.clip(original_img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Save original image
    original_path = os.path.join(debug_dir, "corner_reference_test.png")
    cv2.imwrite(original_path, original_img)
    print(f"Created original corner image: {original_path}")
    
    # Create a test "current" corner image (slightly shifted and rotated)
    current_img = np.ones((600, 800, 3), dtype=np.uint8) * 128  # Gray background
    
    # Draw chip edges (shifted position - 20 pixels right, 10 pixels down)
    cv2.line(current_img, (120, 0), (120, 600), (0, 0, 0), 5)  # Left edge (shifted right)
    cv2.line(current_img, (0, 160), (800, 160), (0, 0, 0), 5)  # Top edge (shifted down)
    
    # Add some noise/texture
    noise = np.random.randint(-30, 30, current_img.shape, dtype=np.int16)
    current_img = np.clip(current_img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Save current image
    current_path = os.path.join(debug_dir, "corner_realignment_test.png")
    cv2.imwrite(current_path, current_img)
    print(f"Created current corner image: {current_path}")
    
    return original_path, current_path

def test_debug_panel():
    """Test the debug panel functionality"""
    try:
        from PyQt6.QtWidgets import QApplication
        from chip_alignment_debug_panel import ChipAlignmentDebugPanel
        
        print("Testing Chip Alignment Debug Panel...")
        
        # Create test images
        original_path, current_path = create_test_corner_images()
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create debug panel
        panel = ChipAlignmentDebugPanel()
        
        # Load test images
        panel.original_image = cv2.imread(original_path)
        panel.current_image = cv2.imread(current_path)
        
        if panel.original_image is not None and panel.current_image is not None:
            # Display images
            panel.display_image(panel.original_image, panel.original_label)
            panel.display_image(panel.current_image, panel.current_label)
            
            panel.status_label.setText("Test images loaded - Ready for analysis!")
            print("✓ Test images loaded successfully")
        else:
            print("✗ Failed to load test images")
            return False
        
        # Show the panel
        panel.show()
        
        print("✓ Debug panel opened successfully")
        print("You can now:")
        print("  1. Click 'Analyze Alignment' to test the analysis")
        print("  2. Load your own corner images using the buttons")
        print("  3. Compare edge detection results")
        
        # Run the application
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"✗ Import error: {str(e)}")
        print("Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        return False

def test_standalone_functionality():
    """Test the debug panel as a standalone application"""
    try:
        from chip_alignment_debug_panel import main
        
        print("Testing standalone debug panel...")
        print("This will open the debug panel in standalone mode.")
        print("You can load corner images and test the analysis functionality.")
        
        # Run the standalone application
        main()
        
    except Exception as e:
        print(f"✗ Standalone test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Chip Alignment Debug Panel")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--standalone":
        test_standalone_functionality()
    else:
        test_debug_panel()

if __name__ == "__main__":
    main()
