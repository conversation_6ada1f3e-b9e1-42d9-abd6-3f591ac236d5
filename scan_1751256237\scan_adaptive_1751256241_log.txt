================================================================================
SCANNING OPERATION LOG - 2025-06-30 12:04:01
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751256237\scan_adaptive_1751256241.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751256237
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751256237\debug_screenshots
================================================================================

[2025-06-30 12:04:01.516] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 12:04:01.527] [INFO] [SYSTEM] Using custom scan folder: scan_1751256237
[2025-06-30 12:04:01.541] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 12:04:01.651] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 12:04:01.661] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 12:04:01.768] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 12:04:02.120] [INFO] [POSITION] Position feedback: (4.23, 0.00) μm
[2025-06-30 12:04:02.846] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 12:04:03.031] [INFO] [POSITION] Position feedback: (345.16, 0.00) μm
[2025-06-30 12:04:03.243] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 12:04:03.591] [INFO] [POSITION] Position feedback: (345.59, 4.02) μm
[2025-06-30 12:04:04.320] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 12:04:04.471] [INFO] [POSITION] Position feedback: (345.59, 244.85) μm
[2025-06-30 12:04:04.982] [INFO] [WORKFLOW] Starting position (rotation-robust): (245.2, 345.6) μm
[2025-06-30 12:04:04.993] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 12:04:05.101] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 12:04:05.112] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 12:04:05.167] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 12:04:05.179] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 12:04:05.404] [INFO] [SUCCESS] ✓ Edge detection validation passed (edges: 6528, lines: H+V)
[2025-06-30 12:04:05.415] [INFO] [STATUS] Creating chip boundary reference from detected edges...
[2025-06-30 12:04:05.425] [INFO] [STATUS] [ChipAlign] Creating chip boundary reference from edge detection results...
[2025-06-30 12:04:05.501] [WARNING] [WARNING] [ChipAlign] Warning: Could not get stage position, using (0,0): tuple indices must be integers or slices, not str
[2025-06-30 12:04:05.511] [INFO] [SUCCESS] [ChipAlign] ✓ Chip boundary reference created successfully
[2025-06-30 12:04:05.521] [INFO] [SUCCESS] ✓ Automatic chip boundary reference created successfully
[2025-06-30 12:04:05.541] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\scan_1751256237\auto_chip_reference_1751256245.json
[2025-06-30 12:04:05.551] [INFO] [SUCCESS] ✓ MANDATORY chip boundary reference creation completed successfully
[2025-06-30 12:04:05.563] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 12:04:05.574] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 12:04:05.584] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 12:04:09.375] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 12:04:09.385] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 12:04:09.510] [INFO] [POSITION] Position feedback: (-4.23, 0.00) μm
[2025-06-30 12:04:10.871] [INFO] [STATUS] Right edge detected: 1/5 right-side points on chip
[2025-06-30 12:04:10.883] [INFO] [STATUS] Row 0: Reached right edge at column 1
[2025-06-30 12:04:10.894] [INFO] [STATUS] Row 0 complete: 1 positions scanned
[2025-06-30 12:04:10.906] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 12:04:11.030] [INFO] [POSITION] Position feedback: (-686.94, -4.23) μm
[2025-06-30 12:04:14.416] [INFO] [WORKFLOW] Finding left edge for row (rotation-robust)
[2025-06-30 12:04:14.760] [INFO] [POSITION] Position feedback: (-4.23, -490.34) μm
[2025-06-30 12:04:15.274] [INFO] [STATUS] Scanning row 1 rightward...
[2025-06-30 12:04:18.541] [INFO] [PROGRESS] Progress: 2/12 (16.7%)
[2025-06-30 12:04:18.551] [INFO] [STATUS] Position (1,0): Found 1 flakes
[2025-06-30 12:04:18.680] [INFO] [POSITION] Position feedback: (-176.71, -490.34) μm
[2025-06-30 12:04:23.376] [INFO] [PROGRESS] Progress: 3/13 (23.1%)
[2025-06-30 12:04:23.386] [INFO] [STATUS] Position (1,1): Found 0 flakes
[2025-06-30 12:04:23.510] [INFO] [POSITION] Position feedback: (-873.59, -490.34) μm
[2025-06-30 12:04:26.978] [INFO] [PROGRESS] Progress: 4/14 (28.6%)
[2025-06-30 12:04:26.987] [INFO] [STATUS] Position (1,2): Found 0 flakes
[2025-06-30 12:04:27.109] [INFO] [POSITION] Position feedback: (-1564.77, -490.34) μm
[2025-06-30 12:04:29.572] [INFO] [PROGRESS] Progress: 5/15 (33.3%)
[2025-06-30 12:04:29.582] [INFO] [STATUS] Position (1,3): Found 0 flakes
[2025-06-30 12:04:29.710] [INFO] [POSITION] Position feedback: (-2250.43, -490.34) μm
[2025-06-30 12:04:33.269] [INFO] [PROGRESS] Progress: 6/16 (37.5%)
[2025-06-30 12:04:33.279] [INFO] [STATUS] Position (1,4): Found 0 flakes
[2025-06-30 12:04:33.409] [INFO] [POSITION] Position feedback: (-2941.61, -490.34) μm
[2025-06-30 12:04:37.757] [INFO] [PROGRESS] Progress: 7/17 (41.2%)
[2025-06-30 12:04:37.766] [INFO] [STATUS] Position (1,5): Found 0 flakes
[2025-06-30 12:04:37.889] [INFO] [POSITION] Position feedback: (-3638.28, -490.34) μm
[2025-06-30 12:04:40.320] [INFO] [PROGRESS] Progress: 8/18 (44.4%)
[2025-06-30 12:04:40.331] [INFO] [STATUS] Position (1,6): Found 0 flakes
[2025-06-30 12:04:40.459] [INFO] [POSITION] Position feedback: (-4323.95, -490.34) μm
[2025-06-30 12:04:42.898] [INFO] [PROGRESS] Progress: 9/19 (47.4%)
[2025-06-30 12:04:42.907] [INFO] [STATUS] Position (1,7): Found 0 flakes
[2025-06-30 12:04:43.029] [INFO] [POSITION] Position feedback: (-5020.41, -490.34) μm
[2025-06-30 12:04:45.396] [INFO] [PROGRESS] Progress: 10/20 (50.0%)
[2025-06-30 12:04:45.405] [INFO] [STATUS] Position (1,8): Found 0 flakes
[2025-06-30 12:04:45.530] [INFO] [POSITION] Position feedback: (-5706.29, -490.34) μm
[2025-06-30 12:04:47.873] [INFO] [PROGRESS] Progress: 11/21 (52.4%)
[2025-06-30 12:04:47.883] [INFO] [STATUS] Position (1,9): Found 0 flakes
[2025-06-30 12:04:48.009] [INFO] [POSITION] Position feedback: (-6397.47, -490.34) μm
[2025-06-30 12:04:50.420] [INFO] [PROGRESS] Progress: 12/22 (54.5%)
[2025-06-30 12:04:50.429] [INFO] [STATUS] Position (1,10): Found 0 flakes
[2025-06-30 12:04:50.559] [INFO] [POSITION] Position feedback: (-7088.64, -490.34) μm
[2025-06-30 12:04:51.903] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 12:04:51.914] [INFO] [STATUS] Row 1: Reached right edge at column 11
[2025-06-30 12:04:51.924] [INFO] [STATUS] Row 1 complete: 11 positions scanned
[2025-06-30 12:04:51.935] [INFO] [WORKFLOW] 
=== Starting Row 2 ===
[2025-06-30 12:04:52.058] [INFO] [POSITION] Position feedback: (-7771.34, -494.15) μm
[2025-06-30 12:04:55.403] [INFO] [STATUS] Reached bottom edge of chip at row 2
[2025-06-30 12:04:55.423] [INFO] [STATUS] 
=== Scan Complete ===
[2025-06-30 12:04:55.433] [INFO] [STATUS] Total positions: 12
[2025-06-30 12:04:55.443] [INFO] [STATUS] Total rows: 2
[2025-06-30 12:04:55.455] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751256237\scan_adaptive_1751256241.csv
[2025-06-30 12:04:55.464] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-06-30 12:04:55.474] [INFO] [STATUS] Scanning for annotated images...
[2025-06-30 12:04:55.485] [INFO] [STATUS] Found 12 annotated images
[2025-06-30 12:04:55.509] [INFO] [STATUS] Processing 1 valid step positions
[2025-06-30 12:04:55.521] [INFO] [STATUS] Grid dimensions: 1 x 1
[2025-06-30 12:04:55.573] [INFO] [STATUS] Creating composite image: 1409 x 1002 pixels
[2025-06-30 12:04:55.615] [ERROR] [ERROR] ⚠ Warning: Could not create composite image: Failed to save composite image
