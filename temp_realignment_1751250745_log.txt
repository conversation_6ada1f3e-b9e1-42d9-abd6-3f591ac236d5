================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:32:25
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751250745.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751243640.json
================================================================================

[2025-06-30 10:32:25.744] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:32:25.754] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:32:25.763] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:32:25.866] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:32:25.875] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:32:25.886] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:32:25.896] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:32:25.905] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:32:25.915] [INFO] [STATUS] [ChipAlign] Using BackgroundEdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:32:25.929] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:32:26.085] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:32:26.096] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:32:26.105] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:32:26.115] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:32:26.165] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:32:27.666] [INFO] [POSITION] Position feedback: (9.73, 0.00) μm
[2025-06-30 10:32:29.432] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:32:29.586] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-06-30 10:32:29.798] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:32:31.115] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:32:31.246] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-06-30 10:32:31.757] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 345.6) μm
[2025-06-30 10:32:31.767] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 345.59) μm
[2025-06-30 10:32:31.776] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:32:31.786] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:32:31.795] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 345.59) μm
[2025-06-30 10:32:31.804] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:32:31.821] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:32:31.830] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:32:31.840] [INFO] [STATUS] Translation: (0.00, 345.59) μm
[2025-06-30 10:32:31.849] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:32:31.858] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:32:31.868] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:32:31.878] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:32:31.897] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:32:31.910] [INFO] [SUCCESS] Successfully transformed 3 flakes
