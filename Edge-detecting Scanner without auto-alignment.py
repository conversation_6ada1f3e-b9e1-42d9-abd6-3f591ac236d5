"""
Integrated Scanning System with Edge Detection
Key fixes for adaptive scanning:
1. find_upper_left_corner now moves to edges by searching, not by calculating from mask
2. Scanning direction: rightward = -Y, downward = -X
3. Proper row-by-row scanning with edge detection
4. Test Edge Detection button for debugging
5. Three edge detection methods:
   - Background Detection: Identifies beige/yellow background to find chip boundaries
   - General Detection: Uses color and edge information
   - No Edge Detection: Manual grid mode with user-specified rows/columns

Terminology:
- Substrate = Silicon chip (gray area where flakes are deposited)
- Background = Beige/yellow tape or material surrounding the chip
"""

import os
import sys
import csv
import time
import ast
import mss
import numpy as np
import cv2
import math
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QPushButton, QProgressBar, QLabel,
    QVBoxLayout, QWidget, QFileDialog, QInputDialog, QHBoxLayout,
    QSplitter, QListWidget, QGraphicsView, QGraphicsScene,
    QAbstractItemView, QGraphicsEllipseItem, QSizePolicy, QGraphicsPolygonItem,
    QMessageBox, QComboBox
)
from PyQt6.QtGui import QPolygonF, QPen, QBrush, QColor, QTransform, QPixmap, QImage
from PyQt6.QtCore import Qt, QPointF, QSize, QThread, pyqtSignal
from inference_sdk import InferenceHTTPClient
from MCM301_COMMAND_LIB import MCM301
import supervision as sv

# --- Constants ---

# STEP_Y_UM = 691.18  # Horizontal step size
# STEP_X_UM = 490.37  # Vertical step size

STEP_Y_UM = 139.56
STEP_X_UM = 99.24

# --- Detection & Annotation Setup ---
detection_client = InferenceHTTPClient(
    api_url="https://detect.roboflow.com",
    api_key="8KXHakThGju0bGhPCTCH"
)
selected_classes = {0, 2, 3}
polygon_annotator = sv.PolygonAnnotator()
label_annotator = sv.LabelAnnotator()

# --- Stage Controller ---
class StageController:
    def __init__(self):
        self.dev = MCM301()
        devs = MCM301.list_devices()
        if not devs:
            raise RuntimeError("No stage found")
        sn = devs[0][0]
        if self.dev.open(sn, 115200, 3) < 0 or not self.dev.is_open(sn):
            raise RuntimeError("Cannot open stage")
    def move_absolute(self, y_um, x_um):
        ctry=[0]; self.dev.convert_nm_to_encoder(5, y_um*1000, ctry)
        ctrx=[0]; self.dev.convert_nm_to_encoder(4, x_um*1000, ctrx)
        self.dev.move_absolute(5, ctry[0])
        self.dev.move_absolute(4, ctrx[0])
    def close(self):
        self.dev.close()

# --- Edge Detection ---
class EdgeDetector:
    """Detects edges of silicon chips based on color/intensity differences"""
    
    def __init__(self, threshold_low=50, threshold_high=150, debug=False):
        self.threshold_low = threshold_low
        self.threshold_high = threshold_high
        self.debug = debug
        
    def detect_chip_edges(self, img):
        """
        Detect silicon chip edges in the image
        Returns: mask where True = chip area, False = outside chip
        """
        # Convert to grayscale
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Method 1: Focus on the chip-background boundary using edge detection
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Use Canny edge detection to find all edges
        edges = cv2.Canny(blurred, 30, 100)
        
        # Method 2: Use color to identify background (beige/yellow)
        if len(img.shape) == 3:
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            # Beige background has specific hue and saturation
            # Typical beige: hue ~20-40, moderate saturation
            lower_background = np.array([15, 20, 100])
            upper_background = np.array([45, 255, 255])
            background_mask = cv2.inRange(hsv, lower_background, upper_background)
            
            # The chip is everything that's NOT background
            chip_mask_color = cv2.bitwise_not(background_mask)
        else:
            # If grayscale, use intensity threshold
            # Background is typically lighter than chip
            _, chip_mask_color = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Method 3: Combine edge and color information
        # Dilate edges to create regions
        kernel = np.ones((10, 10), np.uint8)
        edges_dilated = cv2.dilate(edges, kernel, iterations=2)
        
        # Fill holes in the chip mask
        chip_mask_filled = chip_mask_color.copy()
        h, w = chip_mask_filled.shape
        flood_mask = np.zeros((h + 2, w + 2), np.uint8)
        cv2.floodFill(chip_mask_filled, flood_mask, (0, 0), 255)
        # chip_mask_filled = cv2.bitwise_not(chip_mask_filled)
        
        # Clean up with morphological operations
        kernel_large = np.ones((20, 20), np.uint8)
        chip_mask_cleaned = cv2.morphologyEx(chip_mask_filled, cv2.MORPH_CLOSE, kernel_large)
        chip_mask_cleaned = cv2.morphologyEx(chip_mask_cleaned, cv2.MORPH_OPEN, kernel_large)
        
        # Find the largest connected component (the chip)
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(chip_mask_cleaned)
        if num_labels > 1:
            # Get the largest component (excluding background)
            areas = stats[1:, cv2.CC_STAT_AREA]
            largest_idx = 1 + np.argmax(areas)
            
            # Only accept if it's significantly large (at least 20% of image)
            if areas[largest_idx - 1] > 0.2 * h * w:
                chip_mask = (labels == largest_idx).astype(np.uint8) * 255
            else:
                # If no large component, assume entire image is chip
                chip_mask = np.ones((h, w), np.uint8) * 255
        else:
            chip_mask = chip_mask_cleaned
        
        # Debug visualization
        if self.debug:
            debug_img = img.copy()
            # Draw detected edges in green
            contours, _ = cv2.findContours(chip_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)
            
            # Save intermediate steps
            cv2.imwrite(f"edge_debug_edges_{time.time():.0f}.png", edges)
            cv2.imwrite(f"edge_debug_background_{time.time():.0f}.png", background_mask if len(img.shape) == 3 else np.zeros_like(gray))
            cv2.imwrite(f"edge_debug_chip_mask_{time.time():.0f}.png", chip_mask)
            cv2.imwrite(f"edge_debug_final_{time.time():.0f}.png", debug_img)
        
        return chip_mask
    
    def find_chip_boundaries(self, mask):
        """Find the boundaries of the chip from the mask"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None
        
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        return x, x + w, y, y + h
    
    def is_on_chip(self, img, x, y, margin=10):
        """
        Check if a specific pixel position is on the chip
        margin: pixels from edge to consider as boundary
        """
        mask = self.detect_chip_edges(img)
        h, w = mask.shape
        
        # Check bounds
        if x < 0 or x >= w or y < 0 or y >= h:
            return False
        
        # Check if pixel is on chip (with margin)
        # For edge detection, we'll check a small region around the point
        x_start = max(0, x - margin)
        x_end = min(w, x + margin + 1)
        y_start = max(0, y - margin)
        y_end = min(h, y + margin + 1)
        
        # If the majority of pixels in the margin area are on-chip, consider it on-chip
        region = mask[y_start:y_end, x_start:x_end]
        on_chip_pixels = np.sum(region > 0)
        total_pixels = region.size
        
        # Require at least 80% of pixels to be on-chip
        return on_chip_pixels > 0.8 * total_pixels


class NoEdgeDetector(EdgeDetector):
    """Dummy edge detector that assumes entire image is chip (for debugging)"""
    
    def __init__(self, debug=False):
        super().__init__(debug=debug)
        
    def detect_chip_edges(self, img):
        """Always return full image as chip"""
        h, w = img.shape[:2]
        return np.ones((h, w), np.uint8) * 255
    
    def is_on_chip(self, img, x, y, margin=10):
        """Always return True (entire image is chip)"""
        h, w = img.shape[:2]
        return 0 <= x < w and 0 <= y < h


class BackgroundEdgeDetector(EdgeDetector):
    """Edge detector that focuses on background (beige/yellow tape) detection"""
    
    def __init__(self, debug=False):
        super().__init__(debug=debug)
        
    def detect_chip_edges(self, img):
        """
        Detect chip by identifying background (beige/yellow) areas
        """
        if len(img.shape) != 3:
            # Need color image for background detection
            return np.ones(img.shape[:2], np.uint8) * 255
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # Define multiple ranges for beige/yellow background
        # Range 1: Typical beige
        lower1 = np.array([20, 30, 120])
        upper1 = np.array([35, 100, 255])
        mask1 = cv2.inRange(hsv, lower1, upper1)
        
        # Range 2: Lighter beige/yellow
        lower2 = np.array([15, 20, 150])
        upper2 = np.array([40, 80, 255])
        mask2 = cv2.inRange(hsv, lower2, upper2)
        
        # Combine background masks
        background_mask = cv2.bitwise_or(mask1, mask2)
        
        # Clean up background mask
        kernel = np.ones((5, 5), np.uint8)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_OPEN, kernel)
        background_mask = cv2.morphologyEx(background_mask, cv2.MORPH_CLOSE, kernel)
        
        # Chip is NOT background
        chip_mask = cv2.bitwise_not(background_mask)
        
        # Fill small holes
        kernel_large = np.ones((30, 30), np.uint8)
        chip_mask = cv2.morphologyEx(chip_mask, cv2.MORPH_CLOSE, kernel_large)
        
        # Keep only large connected components
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(chip_mask)
        h, w = chip_mask.shape
        min_area = 0.1 * h * w  # At least 10% of image
        
        final_mask = np.zeros_like(chip_mask)
        for i in range(1, num_labels):
            if stats[i, cv2.CC_STAT_AREA] > min_area:
                final_mask[labels == i] = 255
        
        # If no large components, assume entire image is chip
        if np.sum(final_mask) == 0:
            final_mask = np.ones((h, w), np.uint8) * 255
        
        if self.debug:
            debug_img = img.copy()
            contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 3)
            cv2.imwrite(f"background_debug_{time.time():.0f}.png", debug_img)
            cv2.imwrite(f"background_mask_{time.time():.0f}.png", background_mask)
        
        return final_mask


# --- Unified Scanner Worker ---
class ScanWorker(QThread):
    progress = pyqtSignal(int, int)
    finished = pyqtSignal(str)
    status = pyqtSignal(str)
    
    def __init__(self, mode, x_steps, y_steps, out_csv, region, edge_margin=20, debug=False, edge_method='background'):
        super().__init__()
        self.mode = mode  # 'grid' or 'adaptive'
        self.x_steps = x_steps  # For adaptive+no-edge mode, this is max rows
        self.y_steps = y_steps  # For adaptive+no-edge mode, this is max columns
        self.out_csv = out_csv
        self.region = region
        self.edge_margin = edge_margin
        self.stage = StageController()
        
        # Choose edge detection method
        if mode == 'adaptive':
            if edge_method == 'background':
                self.edge_detector = BackgroundEdgeDetector(debug=debug)
            elif edge_method == 'none':
                self.edge_detector = NoEdgeDetector(debug=debug)
            else:
                self.edge_detector = EdgeDetector(debug=debug)
        else:
            self.edge_detector = None
            
        self.current_origin_x = 0  # Track origin for adaptive scanning
        self.current_origin_y = 0
        
    def process_position(self, img, step_id, step_x, step_y, off_x_um, off_y_um, writer):
        """Common processing for both scan modes"""
        # Run inference
        result = detection_client.infer(img, model_id="raman-fgrub/4")
        data = result if isinstance(result, dict) else result.json()
        
        # Create annotated image
        svd = sv.Detections.from_inference(result)
        svd = svd[np.isin(svd.class_id, list(selected_classes))]
        annotated = polygon_annotator.annotate(scene=img.copy(), detections=svd)
        annotated = label_annotator.annotate(scene=annotated, detections=svd)
        png_path = f"annot_{step_id}.png"
        cv2.imwrite(png_path, annotated)
        
        # Get image dimensions
        h, w, _ = img.shape
        origin_x = w // 2
        origin_y = h // 2
        
        # Calculate conversion factors
        um_per_pixel_vert = STEP_X_UM / h
        um_per_pixel_horiz = STEP_Y_UM / w
        
        # Process detections
        flakes_found = 0
        for pred in data.get('predictions', []):
            if pred['class_id'] in selected_classes:
                cx, cy = pred['x'], pred['y']
                pts = [(pt['x'], pt['y']) for pt in pred['points']]
                
                dx = cx - origin_x
                dy = cy - origin_y
                
                # Calculate real coordinates (swapped axes as confirmed working)
                real_x = off_x_um - dy * um_per_pixel_vert
                real_y = off_y_um - dx * um_per_pixel_horiz
                
                writer.writerow({
                    'step_id': step_id,
                    'step_x': step_x,
                    'step_y': step_y,
                    'pix_origin_x': origin_x,
                    'pix_origin_y': origin_y,
                    'center_x': cx,
                    'center_y': cy,
                    'points': pts,
                    'real_x_um': real_x,
                    'real_y_um': real_y,
                    'class': pred['class'],
                    'detection_id': pred['detection_id']
                })
                flakes_found += 1
        
        return flakes_found
    
    def run_grid_scan(self):
        """Original grid scanning method"""
        total = (self.x_steps + 1) * (self.y_steps + 1)
        count = 0
        
        self.status.emit("Starting grid scan...")
        
        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um', 'class', 'detection_id'
            ])
            writer.writeheader()
            
            for j in range(self.x_steps + 1):
                for k in range(self.y_steps + 1):
                    sid = f"{j}-{k}"
                    
                    off_y_um = -j * STEP_Y_UM
                    off_x_um = -k * STEP_X_UM
                    
                    self.status.emit(f"Moving to grid({j},{k})...")
                    self.stage.move_absolute(off_y_um, off_x_um)
                    time.sleep(1)
                    
                    # Take screenshot
                    with mss.mss() as sct:
                        shot = sct.grab(self.region)
                        img = np.array(shot)
                    
                    # Process position
                    flakes = self.process_position(img, sid, j, k, off_x_um, off_y_um, writer)
                    
                    count += 1
                    self.progress.emit(count, total)
                    self.status.emit(f"Grid({j},{k}): Found {flakes} flakes")
    
    def find_upper_left_corner(self):
        """Find the upper-left corner of the chip and position for scanning"""
        self.status.emit("Finding chip upper-left corner...")
        
        # If using NoEdgeDetector, just use current position as origin
        if isinstance(self.edge_detector, NoEdgeDetector):
            self.status.emit("No edge detection - using current position as origin")
            self.current_origin_x = 0
            self.current_origin_y = 0
            return 0, 0
        
        # First, check current position
        with mss.mss() as sct:
            shot = sct.grab(self.region)
            img = np.array(shot)
        
        h, w, _ = img.shape
        center_x, center_y = w // 2, h // 2
        
        # Check if we're already on the chip
        if not self.edge_detector.is_on_chip(img, center_x, center_y):
            self.status.emit("Not on chip! Please move to chip first.")
            return None
        
        # Move to find the leftmost edge (moving in +Y direction)
        self.status.emit("Finding left edge...")
        current_y = 0
        while True:
            with mss.mss() as sct:
                img = np.array(sct.grab(self.region))
            
            # Check if left side of image is still on chip
            left_check = int(w * 0.2)  # Check 20% from left
            if not self.edge_detector.is_on_chip(img, left_check, center_y):
                # Found left edge, move back a bit
                self.status.emit("Found left edge")
                current_y -= STEP_Y_UM * 0.5  # Move back onto chip
                self.stage.move_absolute(current_y, 0)
                time.sleep(1)
                break
            
            # Move left (+Y direction)
            current_y += STEP_Y_UM * 0.5
            self.stage.move_absolute(current_y,0)
            time.sleep(0.5)
        
        # Now find the top edge (moving in +X direction)
        self.status.emit("Finding top edge...")
        current_x = 0
        while True:
            with mss.mss() as sct:
                img = np.array(sct.grab(self.region))
            
            # Check if top side of image is still on chip
            top_check = int(h * 0.2)  # Check 20% from top
            if not self.edge_detector.is_on_chip(img, center_x, top_check):
                # Found top edge, move back a bit
                self.status.emit("Found top edge")
                current_x -= STEP_X_UM * 0.5  # Move back onto chip
                self.stage.move_absolute(current_y , current_x)
                time.sleep(1)
                break
            
            # Move up (+X direction)
            current_x += STEP_X_UM * 0.5
            self.stage.move_absolute(current_y, current_x)
            time.sleep(0.5)
        
        self.status.emit(f"Starting position: ({current_x:.1f}, {current_y:.1f}) μm")
        self.current_origin_x = current_x
        self.current_origin_y = current_y
        return current_x, current_y
    
    def run_adaptive_scan(self):
        """Adaptive scanning that follows chip edges"""
        # Find starting position
        start = self.find_upper_left_corner()
        if start is None:
            return
        
        self.status.emit("Starting adaptive scan...")
        
        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um', 'class', 'detection_id'
            ])
            writer.writeheader()
            
            row_num = 0
            total_positions = 0
            
            # Main scanning loop - scan rows until we reach bottom edge
            while True:
                self.status.emit(f"\n=== Starting Row {row_num} ===")
                
                # For each row except the first, find the left edge
                if row_num > 0:
                    # Move down one row first
                    current_x = self.current_origin_x - row_num * STEP_X_UM
                    self.stage.move_absolute(self.current_origin_y, current_x)
                    time.sleep(1)
                    
                    # Check if we've moved off the bottom of the chip
                    with mss.mss() as sct:
                        img = np.array(sct.grab(self.region))
                    h, w, _ = img.shape
                    if not self.edge_detector.is_on_chip(img, w//2, h//2):
                        self.status.emit(f"Reached bottom edge of chip at row {row_num}")
                        break
                    
                    # Find left edge for this row (skip for NoEdgeDetector)
                    if not isinstance(self.edge_detector, NoEdgeDetector):
                        self.status.emit(f"Finding left edge for row {row_num}")
                        current_y = self.current_origin_y
                        while True:
                            with mss.mss() as sct:
                                img = np.array(sct.grab(self.region))
                            
                            # Check if we're on the chip
                            if self.edge_detector.is_on_chip(img, w//2, h//2):
                                # Move a bit more onto the chip
                                current_y -= self.edge_margin * (STEP_Y_UM / w)
                                self.stage.move_absolute(current_y, current_x)
                                time.sleep(0.5)
                                break
                            
                            # Move left (+Y direction)
                            current_y += STEP_Y_UM * 0.25
                            self.stage.move_absolute(current_y, current_x)
                            time.sleep(0.5)
                    else:
                        # For NoEdgeDetector, just use origin Y
                        current_y = self.current_origin_y
                else:
                    # First row - we're already at the starting position
                    current_x = self.current_origin_x
                    current_y = self.current_origin_y
                
                # Scan rightward across the row
                col_num = 0
                self.status.emit(f"Scanning row {row_num} rightward...")
                
                while True:
                    # Take screenshot and check if we're still on chip
                    with mss.mss() as sct:
                        shot = sct.grab(self.region)
                        img = np.array(shot)
                    
                    h, w, _ = img.shape
                    
                    # Check if right side is still on chip
                    right_check = int(w * 0.8)  # Check 80% to the right
                    if not self.edge_detector.is_on_chip(img, right_check, h//2):
                        self.status.emit(f"Row {row_num}: Reached right edge at column {col_num}")
                        break
                    
                    # For NoEdgeDetector, limit columns to user-specified value
                    if isinstance(self.edge_detector, NoEdgeDetector) and col_num >= self.y_steps:
                        self.status.emit(f"Row {row_num}: Reached column limit ({self.y_steps})")
                        break
                    
                    # Process current position
                    sid = f"{row_num}-{col_num}"
                    # For adaptive mode, pass absolute positions
                    flakes = self.process_position(img, sid, row_num, col_num, 
                                                 current_x, current_y, writer)
                    
                    total_positions += 1
                    self.progress.emit(total_positions, total_positions + 10)
                    self.status.emit(f"Position ({row_num},{col_num}): Found {flakes} flakes")
                    
                    # Move right (-Y direction)
                    col_num += 1
                    current_y -= STEP_Y_UM
                    self.stage.move_absolute(current_y, current_x)
                    time.sleep(1)
                
                # Finished scanning this row
                self.status.emit(f"Row {row_num} complete: {col_num} positions scanned")
                
                # Move to next row
                row_num += 1
                
                # Safety check - don't scan more than 100 rows
                if row_num > 100:
                    self.status.emit("Safety limit reached (100 rows)")
                    break
                
                # For NoEdgeDetector, use user-specified limit
                if isinstance(self.edge_detector, NoEdgeDetector) and row_num >= self.x_steps:
                    self.status.emit(f"Reached row limit ({self.x_steps} rows)")
                    break
        
        self.status.emit(f"\n=== Scan Complete ===")
        self.status.emit(f"Total positions: {total_positions}")
        self.status.emit(f"Total rows: {row_num}")
    
    def run(self):
        """Main run method that dispatches to appropriate scan type"""
        try:
            if self.mode == 'grid':
                self.run_grid_scan()
            else:  # adaptive
                self.run_adaptive_scan()
            
            self.stage.close()
            self.finished.emit(self.out_csv)
        except Exception as e:
            self.status.emit(f"Error: {str(e)}")
            self.stage.close()
            self.finished.emit("")

# --- Polygon Interaction Items (unchanged) ---
class RotationHandle(QGraphicsEllipseItem):
    def __init__(self,parent,uid,win):
        super().__init__(-5,-5,20,20,parent)
        self.setBrush(QBrush(QColor('black')))
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemIsMovable,True)
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemSendsGeometryChanges,True)
        self.uid=uid; self.win=win; self.parent=parent
    def itemChange(self,chg,val):
        if chg==QGraphicsEllipseItem.GraphicsItemChange.ItemPositionHasChanged:
            c=self.parent.boundingRect().center()
            dx=self.pos().x()-c.x(); dy=self.pos().y()-c.y()
            ang=math.degrees(math.atan2(dy,dx))
            self.parent.setTransform(QTransform().translate(c.x(),c.y()).rotate(ang).translate(-c.x(),-c.y()))
            self.win.flakes[self.uid]['rotation']=ang
        return super().itemChange(chg,val)

class DraggablePolygon(QGraphicsPolygonItem):
    def __init__(self,poly,uid,win):
        super().__init__(poly); self.uid=uid; self.win=win
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemIsMovable,True)
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemSendsGeometryChanges,True)
        rh=RotationHandle(self,uid,win); rh.setPos(self.boundingRect().topRight())
    def itemChange(self,chg,val):
        if chg==QGraphicsPolygonItem.GraphicsItemChange.ItemPositionHasChanged:
            pos=val; base=self.win.flakes[self.uid]
            base['center_px']=(base['center_x']+pos.x(), base['center_y']+pos.y())
        return super().itemChange(chg,val)

# --- Flake Selector GUI (unchanged) ---
class FlakeSelector(QMainWindow):
    def __init__(self, csv_path):
        super().__init__()
        self.flakes = {}
        self.polygon_items = {}
        self.screenshot_map = {}
        self.init_ui()
        self.load_csv(csv_path)

    def init_ui(self):
        self.setWindowTitle('Flake Selector')
        self.resize(1200, 800)
        # Left pane: lists and controls
        self.all_list = QListWidget()
        self.all_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.all_list.itemSelectionChanged.connect(self.update_selected_flakes)
        self.sel_list = QListWidget()
        self.sel_list.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.sel_list.itemClicked.connect(self.display_screenshot)
        self.process_button = QPushButton('Confirm Selection')
        self.process_button.clicked.connect(self.goto)
        self.process_button.setEnabled(False)
        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel('Detected Flakes'))
        left_layout.addWidget(self.all_list)
        left_layout.addWidget(QLabel('Order'))
        left_layout.addWidget(self.sel_list)
        left_layout.addWidget(self.process_button)
        left_widget = QWidget()
        left_widget.setLayout(left_layout)

        # Right pane: top=polygons, bottom=screenshot
        self.flake_view = QGraphicsView()
        self.flake_scene = QGraphicsScene()
        self.flake_view.setScene(self.flake_scene)
        self.flake_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        right_splitter = QSplitter(Qt.Orientation.Vertical)
        right_splitter.addWidget(self.flake_view)
        right_splitter.addWidget(self.image_label)
        right_splitter.setStretchFactor(0, 1)
        right_splitter.setStretchFactor(1, 1)
        right_splitter.setSizes([400, 400])

        main_split = QSplitter(Qt.Orientation.Horizontal)
        main_split.addWidget(left_widget)
        main_split.addWidget(right_splitter)
        main_split.setStretchFactor(1, 1)
        self.setCentralWidget(main_split)

    def load_csv(self, path):
        with open(path) as f:
            for r in csv.DictReader(f):
                sid = r['step_id']
                img_path = f"annot_{sid}.png"
                self.screenshot_map[sid] = img_path
                cx = float(r['center_x']); cy = float(r['center_y'])
                pts = ast.literal_eval(r['points'])
                cl = r['class']; rx = float(r['real_x_um']); ry = float(r['real_y_um'])
                
                pix_origin_x = float(r['pix_origin_x'])
                pix_origin_y = float(r['pix_origin_y'])
                
                uid = f"{sid}_{r['detection_id']}"
                self.flakes[uid] = {
                    'step_id': sid,
                    'center_x': cx,
                    'center_y': cy,
                    'shape': pts,
                    'class': cl,
                    'real_x_um': rx,
                    'real_y_um': ry,
                    'rotation': 0,
                    'pix_origin_x': pix_origin_x,
                    'pix_origin_y': pix_origin_y
                }
                self.all_list.addItem(uid)

    def update_selected_flakes(self):
        uids = [item.text() for item in self.all_list.selectedItems()]
        uids.sort()
        # Update order list
        self.sel_list.clear()
        for uid in uids:
            self.sel_list.addItem(uid)
        self.process_button.setEnabled(bool(uids))
        # Draw polygons
        self.flake_scene.clear()
        materials = list({f['class'] for f in self.flakes.values()})
        colors = [QColor('red'), QColor('blue'), QColor('green'), QColor('yellow')]
        color_map = {cls: colors[i % len(colors)] for i, cls in enumerate(materials)}
        for uid in uids:
            f = self.flakes[uid]
            poly = QPolygonF([QPointF(x, y) for x, y in f['shape']])
            item = DraggablePolygon(poly, uid, self)
            item.setPen(QPen(Qt.GlobalColor.black))
            col = color_map.get(f['class'], QColor('gray'))
            col.setAlpha(100)
            item.setBrush(QBrush(col))
            self.flake_scene.addItem(item)
            self.polygon_items[uid] = item
            # Apply rotation
            rot = f['rotation']
            ctr_pt = item.boundingRect().center()
            item.setTransform(
                QTransform()
                .translate(ctr_pt.x(), ctr_pt.y())
                .rotate(rot)
                .translate(-ctr_pt.x(), -ctr_pt.y())
            )
        if uids:
            self.flake_view.fitInView(self.flake_scene.itemsBoundingRect(), Qt.AspectRatioMode.KeepAspectRatio)

    def display_screenshot(self, item):
        uid = item.text()
        sid = self.flakes[uid]['step_id']
        img_path = self.screenshot_map.get(sid)
        if img_path and os.path.exists(img_path):
            pix = QPixmap(img_path)
            self.image_label.setPixmap(pix.scaled(
                self.image_label.size(), Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))

    def goto(self):
        st = StageController()
        for i in range(self.sel_list.count()):
            uid = self.sel_list.item(i).text()
            f = self.flakes[uid]
            # Swap coordinates as confirmed working
            st.move_absolute(f['real_y_um'], f['real_x_um'])
            time.sleep(0.5)
        st.close()

# --- Main Application ---
class MainApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('Scan & Select')
        
        # Create scan controls
        scan_layout = QHBoxLayout()
        
        # Scan mode selector
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(['Grid Scan', 'Adaptive Chip Scan'])
        scan_layout.addWidget(QLabel('Mode:'))
        scan_layout.addWidget(self.mode_combo)
        
        # Scan button
        btn = QPushButton('Start Scan')
        btn.clicked.connect(self.scan)
        scan_layout.addWidget(btn)
        
        # Test edge detection button
        test_btn = QPushButton('Test Edge Detection')
        test_btn.clicked.connect(self.test_edge_detection)
        scan_layout.addWidget(test_btn)
        
        # Edge detection method selector (for adaptive mode)
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(['Background Detection', 'General Detection', 'No Edge Detection'])
        scan_layout.addWidget(QLabel('Edge Method:'))
        scan_layout.addWidget(self.edge_method_combo)
        
        # Progress and status
        self.pb = QProgressBar()
        self.lb = QLabel('Ready')
        
        # Main layout
        v = QVBoxLayout()
        v.addLayout(scan_layout)
        v.addWidget(self.pb)
        v.addWidget(self.lb)
        
        w = QWidget()
        w.setLayout(v)
        self.setCentralWidget(w)
    
    def test_edge_detection(self):
        """Test edge detection on current view"""
        region = {
            'top': 245,
            'left': 484,
            'width': 1893-484,
            'height': 1247-245
        }
        
        # Take screenshot
        with mss.mss() as sct:
            shot = sct.grab(region)
            img = np.array(shot)
        
        # Test both edge detection methods
        methods = [
            ('General', EdgeDetector(debug=True)),
            ('Background', BackgroundEdgeDetector(debug=True)),
            ('No Edge', NoEdgeDetector(debug=True))
        ]
        
        results = []
        for method_name, detector in methods:
            mask = detector.detect_chip_edges(img)
            
            # Show results
            h, w = mask.shape
            center_x, center_y = w // 2, h // 2
            
            # Check various points
            points_to_check = [
                (center_x, center_y, "Center"),
                (int(w * 0.2), center_y, "Left 20%"),
                (int(w * 0.8), center_y, "Right 80%"),
                (center_x, int(h * 0.2), "Top 20%"),
                (center_x, int(h * 0.8), "Bottom 80%")
            ]
            
            status = f"\n{method_name} Method:\n"
            for x, y, label in points_to_check:
                on_chip = detector.is_on_chip(img, x, y)
                status += f"  {label}: {'ON chip' if on_chip else 'OFF chip'}\n"
            
            results.append(status)
            
            # Save visualization
            vis_img = img.copy()
            # Draw test points
            for x, y, label in points_to_check:
                color = (0, 255, 0) if detector.is_on_chip(img, x, y) else (0, 0, 255)
                cv2.circle(vis_img, (x, y), 10, color, -1)
                cv2.putText(vis_img, label, (x-50, y-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Draw chip boundary
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(vis_img, contours, -1, (255, 255, 0), 2)
            
            cv2.imwrite(f"edge_test_{method_name.lower()}_visualization.png", vis_img)
            cv2.imwrite(f"edge_test_{method_name.lower()}_mask.png", mask)
        
        combined_status = "Edge Detection Test Results:" + ''.join(results)
        combined_status += f"\nSaved visualizations for all methods"
        
        self.lb.setText(combined_status)
        QMessageBox.information(self, "Edge Detection Test", combined_status)
        
    def scan(self):
        mode = 'adaptive' if self.mode_combo.currentIndex() == 1 else 'grid'
        
        # Get parameters based on mode
        if mode == 'grid':
            xs, ok1 = QInputDialog.getInt(self, 'X Steps', 'Number of X steps:', 0, 0)
            ys, ok2 = QInputDialog.getInt(self, 'Y Steps', 'Number of Y steps:', 0, 0)
            if not(ok1 and ok2):
                return
        else:
            # For adaptive mode, check if using no edge detection
            edge_idx = self.edge_method_combo.currentIndex()
            if edge_idx == 2:  # No Edge Detection
                # Get grid dimensions for no-edge mode
                xs, ok1 = QInputDialog.getInt(self, 'Rows', 'Number of rows to scan:', 10, 1, 100)
                ys, ok2 = QInputDialog.getInt(self, 'Columns', 'Number of columns per row:', 10, 1, 100)
                if not(ok1 and ok2):
                    return
            else:
                # For edge detection modes, we don't need step counts
                xs, ys = 0, 0
                
            # Get edge margin
            margin, ok = QInputDialog.getInt(self, 'Edge Margin', 
                                           'Pixels from edge to stop:', 20, 5, 100)
            if not ok:
                return
        
        # Define scan region
        region = {
            'top': 245,
            'left': 484,
            'width': 1893-484,
            'height': 1247-245
        }
        
        # Get output file
        out, _ = QFileDialog.getSaveFileName(self, 'Save CSV', '', '*.csv')
        if not out:
            return
        
        # Create and start worker
        if mode == 'adaptive':
            edge_idx = self.edge_method_combo.currentIndex()
            if edge_idx == 0:
                edge_method = 'background'
            elif edge_idx == 1:
                edge_method = 'general'
            else:
                edge_method = 'none'
                
            self.worker = ScanWorker(mode, xs, ys, out, region, 
                                   edge_margin=margin, debug=True, edge_method=edge_method)
        else:
            self.worker = ScanWorker(mode, xs, ys, out, region)
            
        self.worker.progress.connect(lambda v, t: self.pb.setValue(int(v/t*100)))
        self.worker.status.connect(lambda msg: self.lb.setText(msg))
        self.worker.finished.connect(self.show_selector)
        self.worker.start()
        
        self.lb.setText(f'Starting {mode} scan...')
        
    def show_selector(self, path):
        if path:
            self.lb.setText('Scan complete!')
            self.selector = FlakeSelector(path)
            self.selector.show()
        else:
            self.lb.setText('Scan failed!')
            QMessageBox.warning(self, 'Scan Failed', 
                              'The scan failed. Check the console for errors.')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = MainApp()
    win.show()
    sys.exit(app.exec())