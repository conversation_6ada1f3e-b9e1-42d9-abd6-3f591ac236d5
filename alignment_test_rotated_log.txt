================================================================================
SCANNING OPERATION LOG - 2025-06-30 09:48:53
================================================================================
Mode: adaptive
Output CSV: Z:/A.Members/张恩浩/python/transfer/alignment_test_rotated.csv
Grid Steps: 0 x 0
Edge Method: background
Landmark Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751243640.json
================================================================================

[2025-06-30 09:48:53.518] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 09:48:53.531] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 09:48:53.546] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 09:48:53.709] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 09:48:53.718] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 09:48:53.733] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 09:48:53.743] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 09:48:53.752] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 09:48:53.761] [INFO] [STATUS] [ChipAlign] Using BackgroundEdgeDetector for re-alignment (same as reference creation)
[2025-06-30 09:48:53.771] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 09:48:53.928] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 09:48:53.940] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 09:48:53.950] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 09:48:53.959] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 09:48:54.012] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 09:48:55.688] [INFO] [POSITION] Position feedback: (17.35, 0.00) μm
[2025-06-30 09:48:57.539] [INFO] [POSITION] Position feedback: (349.82, 0.00) μm
[2025-06-30 09:48:59.205] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 09:48:59.358] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 09:48:59.569] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 09:49:00.761] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 09:49:00.899] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-06-30 09:49:01.409] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-06-30 09:49:01.420] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 691.18) μm
[2025-06-30 09:49:01.430] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 09:49:01.441] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 09:49:01.451] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 691.18) μm
[2025-06-30 09:49:01.462] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 09:49:01.476] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 09:49:01.486] [INFO] [SUCCESS] ✓ Re-alignment successful: 0 flakes transformed
[2025-06-30 09:49:01.496] [INFO] [STATUS] Translation: (0.00, 691.18) μm
[2025-06-30 09:49:01.505] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 09:49:01.515] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 09:49:01.534] [INFO] [STATUS] Aligned coordinates saved to: Z:/A.Members/张恩浩/python/transfer/alignment_test_rotated_aligned.csv
[2025-06-30 09:49:01.544] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 09:49:01.554] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Aligned CSV: Z:/A.Members/张恩浩/python/transfer/alignment_test_rotated_aligned.csv
