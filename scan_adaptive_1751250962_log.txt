================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:36:05
================================================================================
Mode: adaptive
Output CSV: Z:/A.Members/张恩浩/python/transfer/scan_adaptive_1751250962.csv
Grid Steps: 0 x 0
Edge Method: background
Chip Alignment: True
Reference File: None
================================================================================

[2025-06-30 10:36:05.483] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:36:05.493] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 10:36:05.598] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 10:36:05.611] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 10:36:05.815] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 10:36:06.198] [INFO] [POSITION] Position feedback: (9.73, 0.00) μm
[2025-06-30 10:36:07.098] [INFO] [POSITION] Position feedback: (349.40, 0.00) μm
[2025-06-30 10:36:07.958] [INFO] [POSITION] Position feedback: (694.98, 0.00) μm
[2025-06-30 10:36:08.688] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 10:36:08.828] [INFO] [POSITION] Position feedback: (1036.12, 0.00) μm
[2025-06-30 10:36:09.039] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 10:36:09.286] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 10:36:09.427] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-06-30 10:36:09.939] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 1036.8) μm
[2025-06-30 10:36:09.950] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 10:36:10.058] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 10:36:10.068] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 10:36:10.112] [INFO] [STATUS] Using BackgroundEdgeDetector for precise edge detection...
[2025-06-30 10:36:10.122] [INFO] [STATUS] Using BackgroundEdgeDetector with basic edge detection...
[2025-06-30 10:36:10.362] [INFO] [SUCCESS] ✓ Edge detection validation passed (edges: 1007, lines: H+V)
[2025-06-30 10:36:10.373] [INFO] [STATUS] Creating chip boundary reference from detected edges...
[2025-06-30 10:36:10.383] [INFO] [STATUS] [ChipAlign] Creating chip boundary reference from edge detection results...
[2025-06-30 10:36:10.468] [WARNING] [WARNING] [ChipAlign] Warning: Could not get stage position, using (0,0): tuple indices must be integers or slices, not str
[2025-06-30 10:36:10.478] [INFO] [SUCCESS] [ChipAlign] ✓ Chip boundary reference created successfully
[2025-06-30 10:36:10.489] [INFO] [SUCCESS] ✓ Automatic chip boundary reference created successfully
[2025-06-30 10:36:10.509] [INFO] [SUCCESS] ✓ Reference saved to: auto_chip_reference_1751250970.json
[2025-06-30 10:36:10.519] [INFO] [SUCCESS] ✓ MANDATORY chip boundary reference creation completed successfully
[2025-06-30 10:36:10.530] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 10:36:10.543] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 10:36:10.555] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 10:36:10.923] [INFO] [STATUS] Right edge detected: 0/5 right-side points on chip
[2025-06-30 10:36:10.935] [INFO] [STATUS] Row 0: Reached right edge at column 0
[2025-06-30 10:36:10.946] [INFO] [STATUS] Row 0 complete: 0 positions scanned
[2025-06-30 10:36:10.957] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-06-30 10:36:11.157] [INFO] [POSITION] Position feedback: (0.00, -17.35) μm
