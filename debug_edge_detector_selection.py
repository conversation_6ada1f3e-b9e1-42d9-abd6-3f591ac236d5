#!/usr/bin/env python3
"""
Debug script for edge detector selection in ChipAlignmentSystem.
"""

from unittest.mock import Mock
from chip_alignment import ChipAlignmentSystem
from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector

def debug_edge_detector_selection():
    """Debug the edge detector selection mechanism."""
    print("=== Debugging Edge Detector Selection ===")
    
    # Create mock stage controller
    mock_stage = Mock()
    mock_stage.get_position.return_value = {'x': 150.0, 'y': 250.0}
    
    # Create ChipAlignmentSystem
    alignment_system = ChipAlignmentSystem(
        mock_stage,
        (100, 100, 800, 600),
        status_callback=lambda x: print(f"Status: {x}")
    )
    
    print("ChipAlignmentSystem created")
    
    # Check what detectors are actually stored
    print(f"self.edge_detector type: {type(alignment_system.edge_detector).__name__}")
    print(f"self.background_edge_detector type: {type(alignment_system.background_edge_detector).__name__}")
    print(f"self.canny_detector type: {type(alignment_system.canny_detector).__name__}")
    print(f"self.precise_edge_detector type: {type(alignment_system.precise_edge_detector).__name__}")
    
    # Test edge detector selection
    detectors_to_test = ['EdgeDetector', 'BackgroundEdgeDetector', 'CannyEdgeDetector']
    
    for detector_name in detectors_to_test:
        print(f"\n--- Testing selection of {detector_name} ---")
        retrieved_detector = alignment_system._get_edge_detector_by_name(detector_name)
        actual_type = type(retrieved_detector).__name__
        print(f"Requested: {detector_name}")
        print(f"Retrieved: {actual_type}")
        print(f"Match: {actual_type == detector_name}")
        
        # Check if it has line fitting capabilities
        has_hough = hasattr(retrieved_detector, 'fit_lines_hough')
        has_ransac = hasattr(retrieved_detector, 'fit_lines_ransac')
        has_hough_fitter = hasattr(retrieved_detector, 'hough_fitter')
        has_ransac_fitter = hasattr(retrieved_detector, 'ransac_fitter')
        
        print(f"Has fit_lines_hough: {has_hough}")
        print(f"Has fit_lines_ransac: {has_ransac}")
        print(f"Has hough_fitter: {has_hough_fitter}")
        print(f"Has ransac_fitter: {has_ransac_fitter}")

if __name__ == "__main__":
    debug_edge_detector_selection()
