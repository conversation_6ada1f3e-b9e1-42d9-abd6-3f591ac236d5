#!/usr/bin/env python3
"""
Test script for the updated re-alignment workflow.
This tests the proper sequence: move to corner -> find corner -> calculate transformation.
"""

import sys
import time
import tempfile
import os
import json
import numpy as np
from unittest.mock import Mock, MagicMock

# Import the functions
from chip_alignment import ChipAlignmentSystem
from edge_detection import BackgroundEdgeDetector, CannyEdgeDetector

def test_realignment_workflow_sequence():
    """Test that the re-alignment workflow follows the correct sequence."""
    print("=== Testing Re-alignment Workflow Sequence ===")
    
    try:
        # Create mock stage controller that tracks movements
        mock_stage = Mock()
        mock_stage.get_position.return_value = {'x': 150.0, 'y': 250.0}
        mock_stage.set_zero = Mock()
        
        # Track move_absolute calls
        move_calls = []
        def track_move_absolute(y_um, x_um):
            move_calls.append((x_um, y_um))  # Store as (x, y) for consistency
            print(f"  Stage moved to: ({x_um:.2f}, {y_um:.2f}) μm")

        mock_stage.move_absolute = Mock(side_effect=track_move_absolute)
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: print(f"  {x}")
        )
        
        # Create test reference data
        reference_data = {
            'format_version': '2.0',
            'alignment_method': 'chip_boundary',
            'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'chip_origin_abs': [100.0, 150.0],
            'boundary_reference': {
                'chip_origin_abs': [100.0, 150.0],
                'left_edge_angle': 90.0,
                'top_edge_angle': 0.0,
                'corner_pixel_coords': [400, 300],
                'confidence_score': 0.95,
                'left_edge_params': None,
                'top_edge_params': None,
                'detection_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'chip_boundary_detection_method': 'hybrid_approach',
                'pixel_to_stage_calibration': None
            },
            'relative_flakes': [
                {'id': 1, 'center_x': 50, 'center_y': 75, 'real_x_um_rel': 50.0, 'real_y_um_rel': 75.0},
                {'id': 2, 'center_x': 150, 'center_y': 125, 'real_x_um_rel': 150.0, 'real_y_um_rel': 125.0}
            ],
            'total_flakes': 2,
            'edge_detection_method': 'BackgroundEdgeDetector',
            'metadata': {'test_mode': True}
        }
        
        # Mock the _find_current_upper_left_corner method to return a slightly offset position
        def mock_find_corner(edge_detector):
            print(f"  Finding corner with {type(edge_detector).__name__}")
            # Simulate finding the corner at a slightly different position
            return (105.0, 155.0)  # 5μm offset from original (100, 150)
        
        alignment_system._find_current_upper_left_corner = mock_find_corner
        
        print("\n--- Testing Re-alignment Workflow ---")
        
        # Perform re-alignment
        result = alignment_system.perform_realignment(reference_data)
        
        # Verify the sequence
        print(f"\n--- Verifying Workflow Sequence ---")
        
        # Check 1: Should have moved to reference position
        if move_calls:
            expected_move = (100.0, 150.0)
            actual_move = move_calls[0]
            if actual_move == expected_move:
                print(f"✅ Step 1: Correctly moved to reference position {expected_move}")
            else:
                print(f"❌ Step 1: Wrong move - expected {expected_move}, got {actual_move}")
                return False
        else:
            print(f"❌ Step 1: No move_absolute calls detected")
            return False
        
        # Check 2: Should have found current corner
        if result.get('success'):
            new_origin = result.get('new_chip_origin')
            if new_origin == (105.0, 155.0):
                print(f"✅ Step 2: Correctly found current corner at {new_origin}")
            else:
                print(f"❌ Step 2: Wrong corner - expected (105.0, 155.0), got {new_origin}")
                return False
        else:
            print(f"❌ Step 2: Re-alignment failed: {result.get('error')}")
            return False
        
        # Check 3: Should have calculated correct transformation
        transformation = result.get('transformation')
        if transformation:
            expected_translation = (5.0, 5.0)  # 105-100, 155-150
            actual_translation = transformation.get('translation')
            if actual_translation == expected_translation:
                print(f"✅ Step 3: Correctly calculated transformation {expected_translation}")
            else:
                print(f"❌ Step 3: Wrong transformation - expected {expected_translation}, got {actual_translation}")
                return False
        else:
            print(f"❌ Step 3: No transformation calculated")
            return False
        
        # Check 4: Should have transformed flakes
        transformed_flakes = result.get('transformed_flakes', [])
        if len(transformed_flakes) == 2:
            print(f"✅ Step 4: Correctly transformed {len(transformed_flakes)} flakes")
            
            # Verify flake transformation
            flake1 = transformed_flakes[0]
            expected_x = 50.0 + 5.0  # relative coordinate + translation
            expected_y = 75.0 + 5.0

            # Check the transformed coordinates (should be in real_x_um_transformed field)
            if (abs(flake1['real_x_um_transformed'] - expected_x) < 0.1 and
                abs(flake1['real_y_um_transformed'] - expected_y) < 0.1):
                print(f"✅ Step 4a: Flake coordinates correctly transformed")
            else:
                print(f"❌ Step 4a: Wrong flake transformation")
                print(f"  Expected: ({expected_x}, {expected_y})")
                print(f"  Got: ({flake1['real_x_um_transformed']}, {flake1['real_y_um_transformed']})")
                return False
        else:
            print(f"❌ Step 4: Wrong number of transformed flakes - expected 2, got {len(transformed_flakes)}")
            return False
        
        print(f"\n🎉 Re-alignment workflow sequence is CORRECT!")
        print(f"✅ Proper sequence: Move to reference → Find current corner → Calculate transformation → Transform flakes")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_detector_selection():
    """Test that re-alignment uses the correct edge detector from reference."""
    print(f"\n=== Testing Edge Detector Selection ===")
    
    try:
        # Create mock stage
        mock_stage = Mock()
        mock_stage.get_position.return_value = {'x': 150.0, 'y': 250.0}
        mock_stage.move_to = Mock()
        
        # Create ChipAlignmentSystem
        alignment_system = ChipAlignmentSystem(
            mock_stage,
            (100, 100, 800, 600),
            status_callback=lambda x: None
        )
        
        # Track which edge detector is used
        used_detectors = []
        
        def mock_find_corner(edge_detector):
            used_detectors.append(type(edge_detector).__name__)
            return (100.0, 150.0)
        
        alignment_system._find_current_upper_left_corner = mock_find_corner
        
        # Test with different edge detectors
        test_cases = [
            ('BackgroundEdgeDetector', 'BackgroundEdgeDetector'),
            ('CannyEdgeDetector', 'CannyEdgeDetector'),
            ('EdgeDetector', 'EdgeDetector')
        ]
        
        for stored_detector, expected_detector in test_cases:
            print(f"\n--- Testing {stored_detector} ---")
            
            reference_data = {
                'boundary_reference': {'chip_origin_abs': [100.0, 150.0]},
                'relative_flakes': [],
                'edge_detection_method': stored_detector
            }
            
            used_detectors.clear()
            result = alignment_system.perform_realignment(reference_data)
            
            if used_detectors and used_detectors[0] == expected_detector:
                print(f"✅ Correctly used {expected_detector}")
            else:
                print(f"❌ Wrong detector - expected {expected_detector}, got {used_detectors}")
                return False
        
        print(f"\n✅ Edge detector selection works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def main():
    """Run all re-alignment workflow tests."""
    print("Testing Updated Re-alignment Workflow")
    print("=" * 60)
    
    tests = [
        test_realignment_workflow_sequence,
        test_edge_detector_selection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            print(f"✗ {test.__name__} CRASHED: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Re-alignment Workflow Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 RE-ALIGNMENT WORKFLOW IS CORRECTLY IMPLEMENTED!")
        print("✅ Proper sequence: Move to reference → Find corner → Transform")
        print("✅ Edge detector selection works correctly")
        print("✅ Transformation calculation is accurate")
        print("✅ Flake coordinate transformation works")
        return True
    else:
        print("\n❌ Some re-alignment workflow tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
