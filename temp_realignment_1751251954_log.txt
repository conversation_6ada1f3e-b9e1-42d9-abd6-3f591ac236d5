================================================================================
SCANNING OPERATION LOG - 2025-06-30 10:52:34
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751251954.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751251088.json
================================================================================

[2025-06-30 10:52:34.879] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 10:52:34.889] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 10:52:34.898] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 10:52:34.993] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 10:52:35.002] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 10:52:35.014] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 10:52:35.024] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 10:52:35.033] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 10:52:35.042] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 10:52:35.055] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 10:52:35.203] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 10:52:35.213] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 10:52:35.225] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 10:52:35.238] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 10:52:35.278] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 10:52:36.823] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-06-30 10:52:38.993] [INFO] [POSITION] Position feedback: (349.61, 0.00) μm
[2025-06-30 10:52:40.983] [INFO] [POSITION] Position feedback: (695.40, 0.00) μm
[2025-06-30 10:52:42.761] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 10:52:42.902] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-06-30 10:52:43.113] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 10:52:44.415] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 10:52:44.542] [INFO] [POSITION] Position feedback: (1036.76, 0.00) μm
[2025-06-30 10:52:45.053] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (0.0, 1036.8) μm
[2025-06-30 10:52:45.063] [INFO] [STATUS] [ChipAlign] Current chip origin: (0.00, 1036.77) μm
[2025-06-30 10:52:45.072] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 10:52:45.082] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 10:52:45.091] [INFO] [STATUS] [ChipAlign] Translation: (0.00, 1036.77) μm
[2025-06-30 10:52:45.099] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 10:52:45.108] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 10:52:45.117] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 10:52:45.130] [INFO] [STATUS] Translation: (0.00, 1036.77) μm
[2025-06-30 10:52:45.140] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 10:52:45.153] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 10:52:45.163] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 10:52:45.174] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 10:52:45.183] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 10:52:45.195] [INFO] [SUCCESS] Successfully transformed 9 flakes
