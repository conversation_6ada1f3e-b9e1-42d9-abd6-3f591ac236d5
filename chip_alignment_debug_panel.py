#!/usr/bin/env python3
"""
Chip Alignment Debug Panel

Visual comparison panel for debugging chip boundary re-alignment workflow.
Displays side-by-side comparison of corner detection screenshots with edge analysis.
"""

import sys
import os
import cv2
import numpy as np
import json
from datetime import datetime

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTextEdit, QGroupBox, QFrame,
                            QScrollArea, QSplitter, QMessageBox, QFileDialog, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QPixmap, QImage, QFont

# Import edge detection classes
from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector
from chip_alignment import ChipAlignmentSystem


class CornerAnalysisWorker(QThread):
    """Worker thread for corner analysis to avoid blocking the GUI"""
    
    analysis_complete = pyqtSignal(dict)
    progress_update = pyqtSignal(str)
    
    def __init__(self, original_image, current_image, edge_detector_name='CannyEdgeDetector'):
        super().__init__()
        self.original_image = original_image
        self.current_image = current_image
        self.edge_detector_name = edge_detector_name
        
    def run(self):
        """Analyze both corner images and calculate transformation"""
        try:
            self.progress_update.emit("Initializing edge detector...")
            
            # Initialize edge detector
            if self.edge_detector_name == 'CannyEdgeDetector':
                detector = CannyEdgeDetector(debug=True)
            elif self.edge_detector_name == 'BackgroundEdgeDetector':
                detector = BackgroundEdgeDetector(debug=True)
            else:
                detector = EdgeDetector(debug=True)
            
            results = {}
            
            # Analyze original image
            self.progress_update.emit("Analyzing original corner image...")
            original_result = self._analyze_corner_image(self.original_image, detector, "original")
            results['original'] = original_result
            
            # Analyze current image
            self.progress_update.emit("Analyzing current corner image...")
            current_result = self._analyze_corner_image(self.current_image, detector, "current")
            results['current'] = current_result
            
            # Calculate transformation
            self.progress_update.emit("Calculating transformation matrix...")
            transformation = self._calculate_transformation(original_result, current_result)
            results['transformation'] = transformation
            
            self.progress_update.emit("Analysis complete!")
            self.analysis_complete.emit(results)
            
        except Exception as e:
            error_result = {'error': str(e)}
            self.analysis_complete.emit(error_result)
    
    def _analyze_corner_image(self, image, detector, label):
        """Analyze a single corner image for edge detection"""
        try:
            if image is None:
                return {'error': f'No {label} image provided'}
            
            # Perform edge detection with line fitting
            result = detector.detect_edges_with_line_fitting(
                image, 
                algorithm_mode='sequential'  # Use the most reliable mode
            )
            
            if not result.get('success', False):
                return {'error': f'Edge detection failed for {label} image'}
            
            # Extract key information
            analysis = {
                'success': True,
                'edges': result.get('edges'),
                'lines': result.get('lines', []),
                'fitted_lines': result.get('fitted_lines', {}),
                'confidence': result.get('confidence', 0.0),
                'processing_time': result.get('processing_time', 0.0),
                'algorithm_mode': result.get('algorithm_mode', 'unknown')
            }
            
            # Calculate corner coordinates if we have fitted lines
            fitted_lines = result.get('fitted_lines', {})
            if fitted_lines.get('horizontal') and fitted_lines.get('vertical'):
                corner_coords = self._calculate_corner_intersection(fitted_lines)
                analysis['corner_coords'] = corner_coords
            
            return analysis
            
        except Exception as e:
            return {'error': f'Analysis failed for {label} image: {str(e)}'}
    
    def _calculate_corner_intersection(self, fitted_lines):
        """Calculate corner intersection from fitted lines"""
        try:
            h_line = fitted_lines.get('horizontal', {})
            v_line = fitted_lines.get('vertical', {})
            
            if not h_line or not v_line:
                return None
            
            # Extract line parameters
            h_slope = h_line.get('slope', 0)
            h_intercept = h_line.get('intercept', 0)
            v_slope = v_line.get('slope', 0)
            v_intercept = v_line.get('intercept', 0)
            
            # Calculate intersection point
            # For horizontal line: y = h_slope * x + h_intercept
            # For vertical line: x = v_slope * y + v_intercept
            # Solving: y = h_slope * (v_slope * y + v_intercept) + h_intercept
            
            if abs(1 - h_slope * v_slope) < 1e-6:
                return None  # Lines are parallel
            
            y = (h_slope * v_intercept + h_intercept) / (1 - h_slope * v_slope)
            x = v_slope * y + v_intercept
            
            return (x, y)
            
        except Exception as e:
            print(f"Corner intersection calculation failed: {str(e)}")
            return None
    
    def _calculate_transformation(self, original_result, current_result):
        """Calculate transformation matrix between original and current corner"""
        try:
            if 'error' in original_result or 'error' in current_result:
                return {'error': 'Cannot calculate transformation due to analysis errors'}
            
            orig_corner = original_result.get('corner_coords')
            curr_corner = current_result.get('corner_coords')
            
            if not orig_corner or not curr_corner:
                return {'error': 'Corner coordinates not available for transformation calculation'}
            
            # Calculate translation (in pixels)
            translation_px = (
                curr_corner[0] - orig_corner[0],
                curr_corner[1] - orig_corner[1]
            )
            
            # Convert to micrometers (approximate conversion)
            # This is a rough estimate - actual conversion depends on camera calibration
            px_to_um_x = 490.37 / 800  # Approximate based on typical setup
            px_to_um_y = 691.18 / 600  # Approximate based on typical setup
            
            translation_um = (
                translation_px[0] * px_to_um_x,
                translation_px[1] * px_to_um_y
            )
            
            # Calculate rotation (simplified - based on edge angles)
            orig_fitted = original_result.get('fitted_lines', {})
            curr_fitted = current_result.get('fitted_lines', {})
            
            rotation_degrees = 0.0
            if (orig_fitted.get('horizontal') and curr_fitted.get('horizontal')):
                orig_angle = orig_fitted['horizontal'].get('angle', 0)
                curr_angle = curr_fitted['horizontal'].get('angle', 0)
                rotation_degrees = curr_angle - orig_angle
            
            return {
                'success': True,
                'translation_px': translation_px,
                'translation_um': translation_um,
                'rotation_degrees': rotation_degrees,
                'original_corner': orig_corner,
                'current_corner': curr_corner
            }
            
        except Exception as e:
            return {'error': f'Transformation calculation failed: {str(e)}'}


class ChipAlignmentDebugPanel(QWidget):
    """Visual comparison panel for chip boundary re-alignment debugging"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('Chip Alignment Debug Panel')
        self.setMinimumSize(1200, 800)
        
        # Data storage
        self.original_image = None
        self.current_image = None
        self.analysis_results = None
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title = QLabel('Chip Boundary Re-alignment Debug Panel')
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("QLabel { font-size: 16px; font-weight: bold; margin: 10px; }")
        layout.addWidget(title)
        
        # Main content area
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side: Image comparison
        image_widget = self.create_image_comparison_widget()
        main_splitter.addWidget(image_widget)
        
        # Right side: Analysis results
        analysis_widget = self.create_analysis_widget()
        main_splitter.addWidget(analysis_widget)
        
        main_splitter.setSizes([800, 400])
        layout.addWidget(main_splitter)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        load_original_btn = QPushButton('Load Original Corner Image')
        load_original_btn.clicked.connect(self.load_original_image)
        button_layout.addWidget(load_original_btn)
        
        load_current_btn = QPushButton('Load Current Corner Image')
        load_current_btn.clicked.connect(self.load_current_image)
        button_layout.addWidget(load_current_btn)
        
        analyze_btn = QPushButton('Analyze Alignment')
        analyze_btn.clicked.connect(self.analyze_alignment)
        analyze_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(analyze_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_image_comparison_widget(self):
        """Create the image comparison widget"""
        widget = QWidget()
        layout = QGridLayout()
        
        # Original image section
        orig_group = QGroupBox("Original Corner (Reference)")
        orig_layout = QVBoxLayout()
        
        self.original_label = QLabel("No image loaded")
        self.original_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.original_label.setMinimumSize(400, 300)
        self.original_label.setStyleSheet("QLabel { border: 2px solid #2196F3; background-color: #f0f0f0; }")
        orig_layout.addWidget(self.original_label)
        
        orig_group.setLayout(orig_layout)
        layout.addWidget(orig_group, 0, 0)
        
        # Current image section
        curr_group = QGroupBox("Current Corner (Re-alignment)")
        curr_layout = QVBoxLayout()
        
        self.current_label = QLabel("No image loaded")
        self.current_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_label.setMinimumSize(400, 300)
        self.current_label.setStyleSheet("QLabel { border: 2px solid #FF9800; background-color: #f0f0f0; }")
        curr_layout.addWidget(self.current_label)
        
        curr_group.setLayout(curr_layout)
        layout.addWidget(curr_group, 0, 1)
        
        widget.setLayout(layout)
        return widget
    
    def create_analysis_widget(self):
        """Create the analysis results widget"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Analysis results
        results_group = QGroupBox("Edge Detection Analysis")
        results_layout = QVBoxLayout()
        
        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setMaximumHeight(200)
        results_layout.addWidget(self.analysis_text)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        # Transformation results
        transform_group = QGroupBox("Transformation Matrix")
        transform_layout = QVBoxLayout()
        
        self.transform_text = QTextEdit()
        self.transform_text.setReadOnly(True)
        self.transform_text.setMaximumHeight(150)
        transform_layout.addWidget(self.transform_text)
        
        transform_group.setLayout(transform_layout)
        layout.addWidget(transform_group)
        
        # Status
        self.status_label = QLabel("Ready - Load corner images to begin analysis")
        self.status_label.setStyleSheet("QLabel { padding: 5px; background-color: #e0e0e0; }")
        layout.addWidget(self.status_label)
        
        widget.setLayout(layout)
        return widget

    def load_original_image(self):
        """Load original corner image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 'Load Original Corner Image', '',
            'Image Files (*.png *.jpg *.jpeg *.bmp);;All Files (*)')

        if file_path:
            try:
                self.original_image = cv2.imread(file_path)
                if self.original_image is not None:
                    self.display_image(self.original_image, self.original_label)
                    self.status_label.setText(f"Original image loaded: {os.path.basename(file_path)}")
                else:
                    QMessageBox.warning(self, 'Error', 'Failed to load original image')
            except Exception as e:
                QMessageBox.critical(self, 'Error', f'Failed to load original image: {str(e)}')

    def load_current_image(self):
        """Load current corner image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 'Load Current Corner Image', '',
            'Image Files (*.png *.jpg *.jpeg *.bmp);;All Files (*)')

        if file_path:
            try:
                self.current_image = cv2.imread(file_path)
                if self.current_image is not None:
                    self.display_image(self.current_image, self.current_label)
                    self.status_label.setText(f"Current image loaded: {os.path.basename(file_path)}")
                else:
                    QMessageBox.warning(self, 'Error', 'Failed to load current image')
            except Exception as e:
                QMessageBox.critical(self, 'Error', f'Failed to load current image: {str(e)}')

    def display_image(self, cv_image, label_widget):
        """Display OpenCV image in QLabel widget"""
        try:
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w

            # Create QImage
            qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)

            # Create pixmap and scale to fit
            pixmap = QPixmap.fromImage(qt_image)
            scaled_pixmap = pixmap.scaled(
                label_widget.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            label_widget.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"Display error: {str(e)}")
            label_widget.setText(f"Display error: {str(e)}")

    def analyze_alignment(self):
        """Start alignment analysis"""
        if self.original_image is None or self.current_image is None:
            QMessageBox.warning(self, 'Missing Images',
                              'Please load both original and current corner images before analysis.')
            return

        self.status_label.setText("Starting analysis...")

        # Start analysis in worker thread
        self.analysis_worker = CornerAnalysisWorker(
            self.original_image,
            self.current_image,
            'CannyEdgeDetector'  # Default to Canny for best results
        )

        self.analysis_worker.progress_update.connect(self.update_status)
        self.analysis_worker.analysis_complete.connect(self.display_analysis_results)
        self.analysis_worker.start()

    def update_status(self, message):
        """Update status label"""
        self.status_label.setText(message)

    def display_analysis_results(self, results):
        """Display analysis results"""
        try:
            if 'error' in results:
                self.analysis_text.setText(f"Analysis Error: {results['error']}")
                self.transform_text.setText("No transformation data available")
                self.status_label.setText("Analysis failed")
                return

            # Display edge detection analysis
            analysis_text = self.format_edge_analysis(results)
            self.analysis_text.setText(analysis_text)

            # Display transformation results
            if 'transformation' in results:
                transform_text = self.format_transformation_analysis(results['transformation'])
                self.transform_text.setText(transform_text)

            # Update images with overlays
            self.update_image_overlays(results)

            self.status_label.setText("Analysis complete - Check results above")

        except Exception as e:
            self.analysis_text.setText(f"Display error: {str(e)}")
            self.status_label.setText("Error displaying results")

    def format_edge_analysis(self, results):
        """Format edge detection analysis for display"""
        text = "=== EDGE DETECTION ANALYSIS ===\n\n"

        for image_type in ['original', 'current']:
            if image_type in results:
                result = results[image_type]
                text += f"--- {image_type.upper()} IMAGE ---\n"

                if 'error' in result:
                    text += f"Error: {result['error']}\n\n"
                    continue

                text += f"Success: {result.get('success', False)}\n"
                text += f"Algorithm: {result.get('algorithm_mode', 'unknown')}\n"
                text += f"Confidence: {result.get('confidence', 0.0):.3f}\n"
                text += f"Processing Time: {result.get('processing_time', 0.0):.3f}s\n"

                # Fitted lines info
                fitted_lines = result.get('fitted_lines', {})
                if fitted_lines:
                    text += "Fitted Lines:\n"
                    for line_type, line_data in fitted_lines.items():
                        if isinstance(line_data, dict):
                            slope = line_data.get('slope', 0)
                            intercept = line_data.get('intercept', 0)
                            angle = line_data.get('angle', 0)
                            text += f"  {line_type}: slope={slope:.3f}, intercept={intercept:.1f}, angle={angle:.1f}°\n"

                # Corner coordinates
                corner = result.get('corner_coords')
                if corner:
                    text += f"Corner Coordinates: ({corner[0]:.1f}, {corner[1]:.1f}) px\n"

                text += "\n"

        return text

    def format_transformation_analysis(self, transform_result):
        """Format transformation analysis for display"""
        text = "=== TRANSFORMATION MATRIX ===\n\n"

        if 'error' in transform_result:
            text += f"Error: {transform_result['error']}\n"
            return text

        text += f"Success: {transform_result.get('success', False)}\n\n"

        # Translation
        trans_px = transform_result.get('translation_px', (0, 0))
        trans_um = transform_result.get('translation_um', (0, 0))
        text += f"Translation (pixels): ({trans_px[0]:.1f}, {trans_px[1]:.1f})\n"
        text += f"Translation (μm): ({trans_um[0]:.2f}, {trans_um[1]:.2f})\n\n"

        # Rotation
        rotation = transform_result.get('rotation_degrees', 0.0)
        text += f"Rotation: {rotation:.2f}°\n\n"

        # Corner positions
        orig_corner = transform_result.get('original_corner')
        curr_corner = transform_result.get('current_corner')
        if orig_corner and curr_corner:
            text += f"Original Corner: ({orig_corner[0]:.1f}, {orig_corner[1]:.1f}) px\n"
            text += f"Current Corner: ({curr_corner[0]:.1f}, {curr_corner[1]:.1f}) px\n"

        return text

    def update_image_overlays(self, results):
        """Update image displays with edge detection overlays"""
        try:
            # Update original image with overlays
            if 'original' in results and self.original_image is not None:
                overlay_img = self.create_overlay_image(self.original_image, results['original'])
                if overlay_img is not None:
                    self.display_image(overlay_img, self.original_label)

            # Update current image with overlays
            if 'current' in results and self.current_image is not None:
                overlay_img = self.create_overlay_image(self.current_image, results['current'])
                if overlay_img is not None:
                    self.display_image(overlay_img, self.current_label)

        except Exception as e:
            print(f"Overlay update error: {str(e)}")

    def create_overlay_image(self, base_image, analysis_result):
        """Create image with edge detection overlays"""
        try:
            overlay_img = base_image.copy()

            # Draw edges if available
            edges = analysis_result.get('edges')
            if edges is not None:
                # Convert edges to 3-channel for overlay
                edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
                edges_colored = cv2.applyColorMap(edges_colored, cv2.COLORMAP_JET)
                overlay_img = cv2.addWeighted(overlay_img, 0.7, edges_colored, 0.3, 0)

            # Draw fitted lines
            fitted_lines = analysis_result.get('fitted_lines', {})
            h, w = overlay_img.shape[:2]

            # Draw horizontal line (blue)
            if 'horizontal' in fitted_lines:
                line_data = fitted_lines['horizontal']
                if isinstance(line_data, dict) and 'slope' in line_data:
                    slope = line_data['slope']
                    intercept = line_data['intercept']
                    y1 = int(slope * 0 + intercept)
                    y2 = int(slope * (w-1) + intercept)
                    cv2.line(overlay_img, (0, y1), (w-1, y2), (255, 0, 0), 3)

            # Draw vertical line (red)
            if 'vertical' in fitted_lines:
                line_data = fitted_lines['vertical']
                if isinstance(line_data, dict) and 'slope' in line_data:
                    slope = line_data['slope']
                    intercept = line_data['intercept']
                    x1 = int(slope * 0 + intercept)
                    x2 = int(slope * (h-1) + intercept)
                    cv2.line(overlay_img, (x1, 0), (x2, h-1), (0, 0, 255), 3)

            # Draw corner point
            corner = analysis_result.get('corner_coords')
            if corner:
                cv2.circle(overlay_img, (int(corner[0]), int(corner[1])), 10, (0, 255, 0), -1)
                cv2.circle(overlay_img, (int(corner[0]), int(corner[1])), 15, (0, 0, 0), 2)

            return overlay_img

        except Exception as e:
            print(f"Overlay creation error: {str(e)}")
            return None


def main():
    """Main function for standalone testing"""
    app = QApplication(sys.argv)

    panel = ChipAlignmentDebugPanel()
    panel.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
