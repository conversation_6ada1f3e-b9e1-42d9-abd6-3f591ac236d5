"""
Configuration constants and settings for the Edge-detecting Scanner
"""

# Step sizes for stage movement
# STEP_Y_UM = 139.56  # Horizontal step size in micrometers
# STEP_X_UM = 99.24   # Vertical step size in micrometers

STEP_Y_UM = 691.18  # Horizontal step size
STEP_X_UM = 490.37  # Vertical step size

# STEP_Y_UM = 691.18/2  # Horizontal step size
# STEP_X_UM = 490.37/2  # Vertical step size

# Detection client configuration
DETECTION_API_URL = "https://detect.roboflow.com"
DETECTION_API_KEY = "8KXHakThGju0bGhPCTCH"
DETECTION_MODEL_ID = "raman-fgrub/4"

# Selected classes for detection
SELECTED_CLASSES = {0, 2, 3}

# Default screen capture region
DEFAULT_REGION = {
    'top': 245,
    'left': 484,
    'width': 1893-484,
    'height': 1247-245
}

# Edge detection parameters
EDGE_DETECTION_PARAMS = {
    'threshold_low': 50,
    'threshold_high': 150,
    'margin_pixels': 20
}

# Alignment parameters
ALIGNMENT_PARAMS = {
    'min_matches': 3,
    'ransac_threshold': 10.0,
    'min_locator_distance_um': 50.0,
    'default_n_locators': 5
}

# Shape matching weights
SHAPE_MATCHING_WEIGHTS = {
    'hu_weight': 0.5,
    'descriptor_weight': 0.3,
    'class_weight': 0.2
}