"""
Enhanced verification script to test the new modular edge detection methods.
This script will show you the detected masks overlaid on the original image so you can visually verify
the enhanced rotation-robust edge detection algorithms.
"""

import mss
import numpy as np
import cv2
import os
import sys
import argparse
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading

# Import the enhanced edge detection methods from the new modular structure
try:
    from edge_detection import EdgeDetector, BackgroundEdgeDetector, NoEdgeDetector, CannyEdgeDetector
    from config import DEFAULT_REGION
    print("Successfully imported enhanced edge detection modules including CannyEdgeDetector!")
except ImportError as e:
    print(f"Error importing enhanced modules: {e}")
    print("Make sure you're running this from the directory with the new modular files.")
    sys.exit(1)

def create_enhanced_overlay_visualization(img, mask, method_name, detector, alpha=0.3):
    """Create an enhanced overlay visualization showing the detected chip area with additional info"""
    # Convert to 3-channel if needed
    if len(img.shape) == 3 and img.shape[2] == 4:
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
    elif len(img.shape) == 2:
        img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

    # Create colored mask (green for chip area)
    colored_mask = np.zeros_like(img)
    colored_mask[mask > 0] = [0, 255, 0]  # Green for chip area

    # Blend the overlay
    result = cv2.addWeighted(img, 1-alpha, colored_mask, alpha, 0)

    # Add method name with enhanced styling
    cv2.putText(result, f"Enhanced {method_name} Method", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 3)
    cv2.putText(result, f"Enhanced {method_name} Method", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)

    # Add chip area percentage
    total_pixels = mask.size
    chip_pixels = np.sum(mask > 0)
    chip_percentage = (chip_pixels / total_pixels) * 100
    cv2.putText(result, f"Chip Area: {chip_percentage:.1f}%", (10, 70),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

    # Add rotation robustness indicator
    cv2.putText(result, "Rotation Robust: YES", (10, 110),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # Test key points and show results - consistent with scanning.py checkpoints
    h, w = mask.shape
    center_x, center_y = w // 2, h // 2
    test_points = [
        (center_x, center_y, "Center"),
        (int(w * 0.2), center_y, "Left 20%"),
        (int(w * 0.8), center_y, "Right 80%"),
        (center_x, int(h * 0.2), "Top 20%"),
        (center_x, int(h * 0.8), "Bottom 80%"),
        (int(w * 0.1), int(h * 0.1), "Top-Left Corner"),
        (int(w * 0.9), int(h * 0.1), "Top-Right Corner"),
        (int(w * 0.1), int(h * 0.9), "Bottom-Left Corner"),
        (int(w * 0.9), int(h * 0.9), "Bottom-Right Corner"),
    ]

    on_chip_count = 0
    for i, (x, y, label) in enumerate(test_points):
        on_chip = detector.is_on_chip(img, x, y)
        color = (0, 255, 0) if on_chip else (0, 0, 255)
        cv2.circle(result, (x, y), 8, color, -1)
        cv2.circle(result, (x, y), 8, (255, 255, 255), 2)
        cv2.putText(result, label, (x-30, y-15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        if on_chip:
            on_chip_count += 1

    # Add coverage score
    coverage = (on_chip_count / len(test_points)) * 100
    cv2.putText(result, f"Coverage: {coverage:.0f}%", (10, 150),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    # Try to get rotation info if available
    try:
        rotated_bounds = detector.find_rotated_chip_boundaries(mask)
        if rotated_bounds and 'angle' in rotated_bounds:
            angle = rotated_bounds['angle']
            cv2.putText(result, f"Detected Angle: {angle:.1f}°", (10, 190),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    except:
        pass  # Method might not be available for all detectors

    return result


def load_image_from_file(filepath):
    """Load and validate image from file with Unicode path support"""
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Image file not found: {filepath}")

    try:
        # Method 1: Try direct OpenCV imread first
        img = cv2.imread(filepath)
        if img is not None:
            print(f"Loaded image from: {filepath}")
            print(f"Image shape: {img.shape}")
            return img
    except:
        pass

    try:
        # Method 2: Use numpy and cv2.imdecode for Unicode paths
        import numpy as np

        # Read file as binary
        with open(filepath, 'rb') as f:
            file_bytes = f.read()

        # Convert to numpy array
        nparr = np.frombuffer(file_bytes, np.uint8)

        # Decode image
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if img is not None:
            print(f"Loaded image from: {filepath} (using Unicode-safe method)")
            print(f"Image shape: {img.shape}")
            return img
    except Exception as e:
        print(f"Unicode method failed: {e}")

    try:
        # Method 3: Copy to temp file with ASCII name
        import tempfile
        import shutil

        # Create temporary file with ASCII name
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            temp_path = tmp_file.name

        # Copy original file to temp location
        shutil.copy2(filepath, temp_path)

        # Try loading from temp file
        img = cv2.imread(temp_path)

        # Clean up temp file
        try:
            os.unlink(temp_path)
        except:
            pass

        if img is not None:
            print(f"Loaded image from: {filepath} (using temporary file method)")
            print(f"Image shape: {img.shape}")
            return img
    except Exception as e:
        print(f"Temporary file method failed: {e}")

    raise ValueError(f"Could not load image from: {filepath}\nThis may be due to Unicode characters in the file path or unsupported image format.")


def create_test_image(output_path="test_chip_safe.png"):
    """Create a test image with simulated chip for testing purposes"""
    try:
        # Create a test image with a simulated chip
        img = np.ones((600, 800, 3), dtype=np.uint8) * 200  # Light background (beige-like)

        # Add a darker 'chip' area (rectangular chip)
        cv2.rectangle(img, (150, 100), (650, 500), (80, 80, 80), -1)

        # Add some 'flakes' on the chip
        cv2.circle(img, (300, 250), 20, (50, 50, 50), -1)  # Dark flake
        cv2.circle(img, (500, 350), 15, (60, 60, 60), -1)  # Another flake
        cv2.ellipse(img, (400, 200), (25, 15), 30, 0, 360, (40, 40, 40), -1)  # Elliptical flake
        cv2.circle(img, (250, 400), 18, (45, 45, 45), -1)  # More flakes
        cv2.ellipse(img, (550, 180), (20, 12), -20, 0, 360, (55, 55, 55), -1)

        # Add some background texture to make it more realistic
        noise = np.random.normal(0, 5, img.shape).astype(np.uint8)
        img = cv2.add(img, noise)

        # Save using the Unicode-safe method
        success = cv2.imwrite(output_path, img)
        if success:
            print(f"Created test image: {output_path}")
            return output_path
        else:
            # Try alternative method
            import tempfile
            temp_dir = tempfile.gettempdir()
            safe_path = os.path.join(temp_dir, "test_chip_safe.png")
            success = cv2.imwrite(safe_path, img)
            if success:
                print(f"Created test image: {safe_path}")
                return safe_path
            else:
                raise ValueError("Failed to create test image")

    except Exception as e:
        print(f"Error creating test image: {e}")
        return None


def capture_screenshot():
    """Capture screenshot from default region"""
    print("Capturing current view...")
    with mss.mss() as sct:
        shot = sct.grab(DEFAULT_REGION)
        img = np.array(shot)

    print(f"Captured image shape: {img.shape}")
    return img


def get_image_source():
    """Interactive function to choose image source"""
    print("\nChoose image source:")
    print("1. Capture screenshot from camera view")
    print("2. Load image from file")
    print("3. Create and use test image")

    while True:
        choice = input("Enter your choice (1, 2, or 3): ").strip()

        if choice == "1":
            return capture_screenshot(), "screenshot"
        elif choice == "2":
            while True:
                filepath = input("Enter path to image file: ").strip()
                if filepath.startswith('"') and filepath.endswith('"'):
                    filepath = filepath[1:-1]  # Remove quotes if present

                try:
                    img = load_image_from_file(filepath)
                    return img, f"file: {os.path.basename(filepath)}"
                except (FileNotFoundError, ValueError) as e:
                    print(f"Error: {e}")
                    print("\nOptions:")
                    print("- Try a different file path")
                    print("- Choose option 3 to create a test image")
                    print("- Press Enter to try again, or type 'back' to return to main menu")

                    retry = input("Your choice: ").strip().lower()
                    if retry == 'back':
                        break
                    elif retry == '':
                        continue
                    else:
                        print("Please try again.")
        elif choice == "3":
            print("Creating test image...")
            test_path = create_test_image()
            if test_path:
                try:
                    img = load_image_from_file(test_path)
                    return img, f"test image: {os.path.basename(test_path)}"
                except Exception as e:
                    print(f"Error loading created test image: {e}")
                    print("Please try another option.")
            else:
                print("Failed to create test image. Please try another option.")
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


def run_verification(img, source_description, output_dir):
    """Run the verification process on the given image"""
    # Save the original image
    original_path = os.path.join(output_dir, "original_image.png")
    cv2.imwrite(original_path, img)
    print(f"Saved original image as '{original_path}'")

    # Test the enhanced methods from the new modular structure
    methods = [
        ('General Enhanced', EdgeDetector(debug=False)),
        ('Background Enhanced', BackgroundEdgeDetector(debug=False)),
        ('Canny Enhanced', CannyEdgeDetector(debug=False)),
        ('No Edge (Debug)', NoEdgeDetector(debug=False)),
    ]

    print(f"\nTesting image from: {source_description}")
    print("\nGenerating enhanced overlay visualizations...")
    print("Features being tested:")
    print("- Multi-scale edge detection")
    print("- Rotation-invariant kernels")
    print("- Enhanced color-based detection")
    print("- Canny edge detection with line fitting")
    print("- Circular morphological operations")
    print("- Rotation-aware boundary detection")
    print()

    results_summary = []

    for method_name, detector in methods:
        print(f"Processing {method_name} method...")

        try:
            # Detect chip edges
            mask = detector.detect_chip_edges(img)

            # Test CannyEdgeDetector's advanced line fitting capabilities
            if isinstance(detector, CannyEdgeDetector):
                print(f"  Testing CannyEdgeDetector line fitting capabilities...")

                # Debug: Save Canny edges to understand the issue
                try:
                    canny_edges = detector.detect_chip_edges_canny(img)
                    canny_debug_path = os.path.join(output_dir, f"canny_edges_debug.png")
                    cv2.imwrite(canny_debug_path, canny_edges)
                    print(f"    - Saved Canny edges debug image: {canny_debug_path}")

                    # Check edge statistics
                    edge_pixels = np.sum(canny_edges > 0)
                    total_pixels = canny_edges.size
                    edge_percentage = (edge_pixels / total_pixels) * 100
                    print(f"    - Canny edge pixels: {edge_percentage:.2f}% of image")
                except Exception as e:
                    print(f"    - Canny edge debug failed: {str(e)}")

                # Test different algorithm modes
                for mode in ['sequential', 'hough_only', 'ransac_only']:
                    try:
                        line_result = detector.detect_edges_with_line_fitting(img, algorithm_mode=mode)
                        if line_result and 'processing_time' in line_result:
                            print(f"    - {mode} mode: {line_result['processing_time']:.3f}s")
                        else:
                            print(f"    - {mode} mode: completed")
                    except Exception as e:
                        print(f"    - {mode} mode: failed ({str(e)})")

            # Create enhanced overlay visualization
            overlay = create_enhanced_overlay_visualization(img, mask, method_name, detector)

            # Save overlay
            overlay_path = os.path.join(output_dir, f"{method_name.lower().replace(' ', '_')}_overlay.png")
            cv2.imwrite(overlay_path, overlay)
            print(f"✓ Saved {method_name} overlay as '{overlay_path}'")

            # Also save the raw mask for reference
            mask_path = os.path.join(output_dir, f"{method_name.lower().replace(' ', '_')}_mask.png")
            cv2.imwrite(mask_path, mask)

            # Calculate statistics
            total_pixels = mask.size
            chip_pixels = np.sum(mask > 0)
            chip_percentage = (chip_pixels / total_pixels) * 100

            # Test key points - consistent with scanning.py checkpoints
            h, w = mask.shape
            center_x, center_y = w // 2, h // 2
            test_points = [
                (center_x, center_y),           # Center
                (int(w * 0.2), center_y),       # Left 20%
                (int(w * 0.8), center_y),       # Right 80%
                (center_x, int(h * 0.2)),       # Top 20%
                (center_x, int(h * 0.8)),       # Bottom 80%
                (int(w * 0.1), int(h * 0.1)),   # Top-Left Corner
                (int(w * 0.9), int(h * 0.1)),   # Top-Right Corner
                (int(w * 0.1), int(h * 0.9)),   # Bottom-Left Corner
                (int(w * 0.9), int(h * 0.9)),   # Bottom-Right Corner
            ]
            on_chip_count = sum(1 for x, y in test_points if detector.is_on_chip(img, x, y))
            coverage = (on_chip_count / len(test_points)) * 100

            results_summary.append({
                'method': method_name,
                'chip_percentage': chip_percentage,
                'coverage': coverage
            })

            print(f"  - Chip area: {chip_percentage:.1f}%")
            print(f"  - Point coverage: {coverage:.0f}%")

        except Exception as e:
            print(f"✗ Error processing {method_name}: {e}")
            continue

    return results_summary


class VerificationGUI:
    """Built-in GUI for Edge Detection Verification"""

    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Edge Detection Verification Tool")
        self.root.geometry("700x600")

        # Variables
        self.selected_file = tk.StringVar()
        self.output_dir = tk.StringVar(value="enhanced_verification_results")
        self.status_text = tk.StringVar(value="Ready - Select an image source to begin")

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="Enhanced Edge Detection Verification Tool",
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))

        # Image source selection
        source_frame = ttk.LabelFrame(main_frame, text="Image Source", padding="15")
        source_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        # Buttons in a row
        btn_frame = ttk.Frame(source_frame)
        btn_frame.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        screenshot_btn = ttk.Button(btn_frame, text="📷 Capture Screenshot",
                                   command=self.capture_screenshot, width=20)
        screenshot_btn.grid(row=0, column=0, padx=(0, 10))

        file_btn = ttk.Button(btn_frame, text="📁 Select Image File",
                             command=self.select_file, width=20)
        file_btn.grid(row=0, column=1, padx=10)

        test_btn = ttk.Button(btn_frame, text="🧪 Create Test Image",
                             command=self.create_test_image, width=20)
        test_btn.grid(row=0, column=2, padx=(10, 0))

        # Selected file display
        file_info_frame = ttk.Frame(source_frame)
        file_info_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(file_info_frame, text="Selected:").grid(row=0, column=0, sticky=tk.W)
        file_label = ttk.Label(file_info_frame, textvariable=self.selected_file,
                              foreground="blue", font=("Arial", 10))
        file_label.grid(row=1, column=0, sticky=tk.W)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="15")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Label(settings_frame, text="Output directory:").grid(row=0, column=0, sticky=tk.W)
        output_entry = ttk.Entry(settings_frame, textvariable=self.output_dir, width=50)
        output_entry.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))

        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=3, column=0, columnspan=3, pady=(0, 15))

        run_btn = ttk.Button(action_frame, text="🔍 Run Verification",
                            command=self.run_verification, width=20)
        run_btn.grid(row=0, column=0, padx=(0, 15))

        clear_btn = ttk.Button(action_frame, text="🗑️ Clear Results",
                              command=self.clear_results, width=15)
        clear_btn.grid(row=0, column=1, padx=15)

        help_btn = ttk.Button(action_frame, text="❓ Help",
                             command=self.show_help, width=10)
        help_btn.grid(row=0, column=2, padx=(15, 0))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        # Status
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Label(status_frame, text="Status:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        status_label = ttk.Label(status_frame, textvariable=self.status_text,
                                foreground="green", font=("Arial", 10))
        status_label.grid(row=1, column=0, sticky=tk.W)

        # Results text area
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))

        self.results_text = tk.Text(results_frame, height=15, width=80, wrap=tk.WORD,
                                   font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        settings_frame.columnconfigure(1, weight=1)

    def select_file(self):
        """Open file dialog to select an image file"""
        filetypes = [
            ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.tif"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Select Image File for Edge Detection Testing",
            filetypes=filetypes
        )

        if filename:
            self.selected_file.set(f"File: {os.path.basename(filename)}")
            self.filepath = filename
            self.status_text.set(f"Selected file: {os.path.basename(filename)}")
            self.log_message(f"Selected file: {filename}")

    def capture_screenshot(self):
        """Capture screenshot from camera view"""
        try:
            self.status_text.set("Screenshot captured - ready to run verification")
            self.selected_file.set("Screenshot from camera view")
            self.filepath = "screenshot"
            self.log_message("Screenshot will be captured when verification runs")
        except Exception as e:
            self.status_text.set(f"Error: {str(e)}")
            messagebox.showerror("Screenshot Error", f"Failed to prepare screenshot:\n{str(e)}")

    def create_test_image(self):
        """Create a test image for verification"""
        try:
            self.status_text.set("Creating test image...")
            test_path = create_test_image()

            if test_path:
                self.selected_file.set(f"Test image: {os.path.basename(test_path)}")
                self.filepath = test_path
                self.status_text.set(f"Test image created: {os.path.basename(test_path)}")
                self.log_message(f"Created test image: {test_path}")
                messagebox.showinfo("Test Image Created",
                                  f"Test image created successfully!\n\n"
                                  f"Location: {test_path}\n"
                                  f"This image contains a simulated chip with flakes for testing.")
            else:
                self.status_text.set("Failed to create test image")
                messagebox.showerror("Test Image Error", "Failed to create test image")

        except Exception as e:
            self.status_text.set(f"Error creating test image: {str(e)}")
            messagebox.showerror("Test Image Error", f"Failed to create test image:\n{str(e)}")

    def run_verification(self):
        """Run the edge detection verification in a separate thread"""
        if not hasattr(self, 'filepath') or not self.filepath:
            messagebox.showwarning("No Image Selected",
                                 "Please select an image file, capture a screenshot, or create a test image first.")
            return

        # Run in separate thread to avoid blocking GUI
        thread = threading.Thread(target=self._run_verification_thread)
        thread.daemon = True
        thread.start()

    def _run_verification_thread(self):
        """Run verification in background thread"""
        try:
            self.progress.start()
            self.status_text.set("Running edge detection verification...")

            # Clear previous results
            self.results_text.delete(1.0, tk.END)

            # Create output directory
            output_dir = self.output_dir.get()
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Get image
            if self.filepath == "screenshot":
                img = capture_screenshot()
                source_description = "screenshot"
                self.log_message("Using captured screenshot")
            else:
                img = load_image_from_file(self.filepath)
                source_description = f"file: {os.path.basename(self.filepath)}"
                self.log_message(f"Loaded image from file: {self.filepath}")

            # Run verification
            self.log_message("Running enhanced edge detection verification...")
            results_summary = run_verification(img, source_description, output_dir)

            # Display results
            self.display_results(results_summary, output_dir, source_description)

            self.status_text.set("✓ Verification completed successfully!")

        except Exception as e:
            self.status_text.set(f"✗ Error: {str(e)}")
            self.log_message(f"ERROR: {str(e)}")
            messagebox.showerror("Verification Error", f"Verification failed:\n{str(e)}")
        finally:
            self.progress.stop()

    def display_results(self, results_summary, output_dir, source_description):
        """Display verification results in the text area"""
        self.log_message("\n" + "="*60)
        self.log_message("ENHANCED VERIFICATION COMPLETE!")
        self.log_message("="*60)

        self.log_message(f"\nTested image from: {source_description}")
        self.log_message(f"Results saved in: {output_dir}")

        self.log_message("\nFiles created:")
        self.log_message("- original_image.png: The input image")
        self.log_message("- *_overlay.png: Enhanced overlay visualizations with test points")
        self.log_message("- *_mask.png: Raw detection masks")

        self.log_message("\nResults Summary:")
        for result in results_summary:
            self.log_message(f"- {result['method']}: {result['chip_percentage']:.1f}% chip area, {result['coverage']:.0f}% coverage")

        self.log_message("\nEnhanced Features Visualization:")
        self.log_message("✓ Green overlay = Detected chip area")
        self.log_message("✓ Colored circles = Test points (Green=on chip, Red=off chip)")
        self.log_message("✓ Coverage percentage = How well the method detects chip boundaries")
        self.log_message("✓ Rotation angle = Detected chip orientation (if available)")

        self.log_message("\nRecommendations:")
        if results_summary:
            best_method = max(results_summary, key=lambda x: x['coverage'])
            self.log_message(f"• Best performing method: {best_method['method']}")
        self.log_message("• For rotated chips >20°, use 'Background Enhanced' method")
        self.log_message("• For precise edge detection, use 'Canny Enhanced' method")
        self.log_message("• Check overlay images to visually verify detection accuracy")

        # Results folder info
        abs_path = os.path.abspath(output_dir)
        self.log_message(f"\n📁 Results folder: {abs_path}")

        # Open folder button
        self.log_message("\n" + "="*60)
        self.log_message("Click 'Open Results Folder' below to view the generated images")

        # Add button to open results folder
        self.root.after(100, lambda: self.add_open_folder_button(abs_path))

    def add_open_folder_button(self, folder_path):
        """Add button to open results folder"""
        try:
            open_btn = ttk.Button(self.root, text="📁 Open Results Folder",
                                 command=lambda: self.open_folder(folder_path))
            # Find the main frame and add button
            for child in self.root.winfo_children():
                if isinstance(child, ttk.Frame):
                    open_btn.grid(row=7, column=0, pady=10)
                    break
        except:
            pass  # If button creation fails, just continue

    def open_folder(self, folder_path):
        """Open the results folder in file explorer"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("Error", f"Could not open folder:\n{str(e)}")

    def clear_results(self):
        """Clear the results text area"""
        self.results_text.delete(1.0, tk.END)
        self.status_text.set("Results cleared - ready for new verification")

    def show_help(self):
        """Show help dialog"""
        help_text = """Enhanced Edge Detection Verification Tool

This tool tests enhanced edge detection methods with rotation robustness.

How to use:
1. Select an image source:
   • 📷 Capture Screenshot: Use current camera view
   • 📁 Select Image File: Choose an image file from disk
   • 🧪 Create Test Image: Generate a sample chip image for testing

2. Configure output directory (optional)

3. Click 🔍 Run Verification to start testing

The tool will test four enhanced methods:
• General Enhanced: Multi-scale edge detection
• Background Enhanced: Color-based detection (best for rotated chips)
• Canny Enhanced: Canny edge detection with line fitting (precise detection)
• No Edge (Debug): Assumes entire image is chip

Results include:
• Overlay images with visual feedback
• Coverage statistics
• Rotation robustness analysis

For best results with rotated chips (>20°), use the Background Enhanced method.

The tool now supports Unicode file paths and provides multiple input options
for maximum flexibility and ease of use.
"""
        messagebox.showinfo("Help", help_text)

    def log_message(self, message):
        """Add message to results text area"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()


def launch_gui():
    """Launch the GUI version"""
    root = tk.Tk()

    # Set up modern styling
    style = ttk.Style()
    try:
        style.theme_use('vista')  # Windows modern theme
    except:
        try:
            style.theme_use('clam')  # Cross-platform modern theme
        except:
            pass  # Use default theme

    app = VerificationGUI(root)

    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    # Make window resizable
    root.minsize(700, 600)

    print("GUI launched successfully!")
    root.mainloop()


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Enhanced Edge Detection Verification Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python verify_edge_detection.py                    # GUI mode (default)
  python verify_edge_detection.py --gui              # Explicit GUI mode
  python verify_edge_detection.py --no-gui           # Command line interactive mode
  python verify_edge_detection.py --file image.png   # Test specific file (CLI)
  python verify_edge_detection.py --screenshot       # Force screenshot mode (CLI)
  python verify_edge_detection.py --test-image       # Create and test with sample image (CLI)
        """
    )

    parser.add_argument('--file', '-f', type=str,
                       help='Test with specific image file instead of screenshot')
    parser.add_argument('--screenshot', '-s', action='store_true',
                       help='Force screenshot mode (skip interactive choice)')
    parser.add_argument('--test-image', '-t', action='store_true',
                       help='Create and use a test image for verification')
    parser.add_argument('--output-dir', '-o', type=str, default="enhanced_verification_results",
                       help='Output directory for results (default: enhanced_verification_results)')
    parser.add_argument('--gui', '-g', action='store_true',
                       help='Launch GUI mode (default if no other options specified)')
    parser.add_argument('--no-gui', action='store_true',
                       help='Force command line mode (disable GUI)')

    args = parser.parse_args()

    # Create output directory
    output_dir = args.output_dir
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    print("Enhanced Edge Detection Verification Tool")
    # Check if GUI mode should be used (default behavior)
    use_gui = not args.no_gui and not any([args.file, args.screenshot, args.test_image])

    if args.gui or use_gui:
        # Launch GUI mode
        print("Launching GUI mode...")
        launch_gui()
        return

    # Command line mode
    print("Testing Enhanced Edge Detection Methods with Rotation Robustness")
    print("=" * 70)

    # Determine image source based on arguments
    if args.file:
        # Use specified file
        try:
            img = load_image_from_file(args.file)
            source_description = f"file: {os.path.basename(args.file)}"
        except (FileNotFoundError, ValueError) as e:
            print(f"Error loading file: {e}")
            print("Tip: Try using --test-image to create a sample image for testing")
            return
    elif args.screenshot:
        # Force screenshot mode
        img = capture_screenshot()
        source_description = "screenshot"
    elif args.test_image:
        # Create and use test image
        print("Creating test image...")
        test_path = create_test_image()
        if test_path:
            try:
                img = load_image_from_file(test_path)
                source_description = f"test image: {os.path.basename(test_path)}"
            except Exception as e:
                print(f"Error loading created test image: {e}")
                return
        else:
            print("Failed to create test image")
            return
    else:
        # Interactive mode
        img, source_description = get_image_source()

    # Run the verification process
    results_summary = run_verification(img, source_description, output_dir)

    print(f"\n" + "=" * 70)
    print("ENHANCED VERIFICATION COMPLETE!")
    print("=" * 70)

    print(f"\nResults saved in '{output_dir}':")
    print("- original_image.png: The captured image")
    print("- *_overlay.png: Enhanced overlay visualizations with test points")
    print("- *_mask.png: Raw detection masks")

    print("\nResults Summary:")
    for result in results_summary:
        print(f"- {result['method']}: {result['chip_percentage']:.1f}% chip area, {result['coverage']:.0f}% coverage")

    print("\nEnhanced Features Visualization:")
    print("✓ Green overlay = Detected chip area")
    print("✓ Colored circles = Test points (Green=on chip, Red=off chip)")
    print("✓ Coverage percentage = How well the method detects chip boundaries")
    print("✓ Rotation angle = Detected chip orientation (if available)")

    print("\nInstructions:")
    print("1. Look at the overlay images where green areas show detected chip regions")
    print("2. Check the colored test points - green should be on chip, red off chip")
    print("3. Compare coverage percentages - higher is generally better")
    print("4. The 'Background Enhanced' method should work best for rotated chips")
    print("5. The 'Canny Enhanced' method should provide precise edge detection")
    print("6. Green overlay should cover the chip area and NOT cover the background tape")

    print(f"\nFor rotation testing, try rotating your chip >20° and run again!")
    print("The enhanced methods should maintain good detection even with rotation.")

if __name__ == "__main__":
    main()
