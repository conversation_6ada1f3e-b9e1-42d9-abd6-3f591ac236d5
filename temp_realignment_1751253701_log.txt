================================================================================
SCANNING OPERATION LOG - 2025-06-30 11:21:41
================================================================================
Mode: adaptive
Output CSV: temp_realignment_1751253701.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: Z:/A.Members/张恩浩/python/transfer/auto_chip_reference_1751251088.json
================================================================================

[2025-06-30 11:21:41.780] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 11:21:41.793] [INFO] [STATUS] === CHIP BOUNDARY RE-ALIGNMENT MODE ===
[2025-06-30 11:21:41.803] [INFO] [CALIBRATION] Setting initial zero reference point for re-alignment...
[2025-06-30 11:21:41.921] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully for re-alignment
[2025-06-30 11:21:41.931] [INFO] [WORKFLOW] Starting quick chip boundary re-alignment...
[2025-06-30 11:21:41.943] [INFO] [STATUS] Loaded chip boundary reference with 0 flakes
[2025-06-30 11:21:41.952] [INFO] [WORKFLOW] [ChipAlign] Starting chip boundary re-alignment...
[2025-06-30 11:21:41.965] [INFO] [STATUS] [ChipAlign] Reference chip origin: (0.00, 0.00) μm
[2025-06-30 11:21:41.978] [INFO] [STATUS] [ChipAlign] Using EdgeDetector for re-alignment (same as reference creation)
[2025-06-30 11:21:41.988] [INFO] [STATUS] [ChipAlign] Moving to reference upper-left corner position...
[2025-06-30 11:21:42.131] [INFO] [POSITION] Position feedback: (0.00, 0.00) μm
[2025-06-30 11:21:42.141] [INFO] [STATUS] [ChipAlign] Moved to reference position: (0.00, 0.00) μm
[2025-06-30 11:21:42.155] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using edge detection...
[2025-06-30 11:21:42.167] [INFO] [WORKFLOW] [ChipAlign] Finding current upper-left corner using proven rotation-robust method...
[2025-06-30 11:21:42.210] [INFO] [WORKFLOW] [ChipAlign] Finding left edge (rotation-robust)...
[2025-06-30 11:21:43.630] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-06-30 11:21:45.407] [INFO] [STATUS] [ChipAlign] Found left edge (rotation-robust)
[2025-06-30 11:21:45.620] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-06-30 11:21:45.831] [INFO] [WORKFLOW] [ChipAlign] Finding top edge (rotation-robust)...
[2025-06-30 11:21:47.281] [INFO] [POSITION] Position feedback: (345.59, 9.52) μm
[2025-06-30 11:21:49.005] [INFO] [STATUS] [ChipAlign] Found top edge (rotation-robust)
[2025-06-30 11:21:49.150] [INFO] [POSITION] Position feedback: (345.59, 245.06) μm
[2025-06-30 11:21:49.661] [INFO] [WORKFLOW] [ChipAlign] Starting position (rotation-robust): (245.2, 345.6) μm
[2025-06-30 11:21:49.940] [INFO] [STATUS] [ChipAlign] Corner screenshot saved: debug_screenshots\corner_realignment_1751253709.png
[2025-06-30 11:21:49.949] [INFO] [STATUS] [ChipAlign] Corner position: (245.19, 345.59) μm
[2025-06-30 11:21:49.960] [INFO] [STATUS] [ChipAlign] Current chip origin: (245.19, 345.59) μm
[2025-06-30 11:21:49.969] [INFO] [STATUS] [ChipAlign] Calculating transformation matrix...
[2025-06-30 11:21:49.979] [INFO] [STATUS] [ChipAlign] Re-alignment completed: 0 flakes transformed
[2025-06-30 11:21:49.988] [INFO] [STATUS] [ChipAlign] Translation: (245.19, 345.59) μm
[2025-06-30 11:21:49.997] [INFO] [STATUS] [ChipAlign] Rotation: 0.00°
[2025-06-30 11:21:50.008] [INFO] [STATUS] [ChipAlign] Confidence: 0.800
[2025-06-30 11:21:50.018] [INFO] [SUCCESS] ✓ Re-alignment successful!
[2025-06-30 11:21:50.032] [INFO] [STATUS] Translation: (245.19, 345.59) μm
[2025-06-30 11:21:50.043] [INFO] [STATUS] Rotation: 0.00°
[2025-06-30 11:21:50.054] [INFO] [STATUS] Confidence: 0.800
[2025-06-30 11:21:50.065] [INFO] [SUCCESS] ✓ Quick chip boundary re-alignment completed successfully!
[2025-06-30 11:21:50.075] [INFO] [WORKFLOW] CHIP-REALIGNMENT workflow completed - SUCCESS - Transformation matrix calculated
[2025-06-30 11:21:50.086] [INFO] [STATUS] Loading CSV file: Z:/A.Members/张恩浩/python/transfer/alignment_test.csv
[2025-06-30 11:21:50.099] [INFO] [SUCCESS] Successfully transformed 9 flakes
