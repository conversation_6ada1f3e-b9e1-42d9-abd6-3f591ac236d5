"""
Alignment Module for Chip Auto-Alignment

This module contains classes for flake shape analysis, matching, and coordinate transformation.
Enhanced with rotation-robust algorithms for handling highly rotated chips.
"""

import cv2
import numpy as np
import time
import json
from dataclasses import dataclass
from typing import List, Tuple, Dict, Optional
from config import ALIGNMENT_PARAMS, SHAPE_MATCHING_WEIGHTS


@dataclass
class Flake:
    """Data structure for a flake with enhanced shape analysis capabilities"""
    id: str
    center_x: float
    center_y: float
    real_x_um: float
    real_y_um: float
    shape: List[Tuple[float, float]]
    class_name: str
    area: float = 0.0
    perimeter: float = 0.0
    hu_moments: np.ndarray = None
    shape_descriptor: np.ndarray = None
    rotation_invariant_features: np.ndarray = None

    def __post_init__(self):
        """Calculate shape properties after initialization"""
        if self.shape:
            self.calculate_shape_properties()

    def calculate_shape_properties(self):
        """Calculate area, perimeter, and enhanced shape descriptors"""
        pts = np.array(self.shape, dtype=np.float32)

        self.area = cv2.contourArea(pts)
        self.perimeter = cv2.arcLength(pts, True)

        # Enhanced Hu moments calculation
        moments = cv2.moments(pts)
        self.hu_moments = cv2.HuMoments(moments).flatten()

        # Basic shape descriptor
        x, y, w, h = cv2.boundingRect(pts)
        aspect_ratio = w / h if h > 0 else 1.0
        circularity = 4 * np.pi * self.area / (self.perimeter ** 2) if self.perimeter > 0 else 0

        # Enhanced rotation-invariant features
        self._calculate_rotation_invariant_features(pts, moments)

        self.shape_descriptor = np.array([
            self.area / 1000.0,
            self.perimeter / 100.0,
            aspect_ratio,
            circularity
        ])

    def _calculate_rotation_invariant_features(self, pts, moments):
        """Calculate additional rotation-invariant features"""
        # Normalized central moments (rotation invariant)
        if moments['m00'] > 0:
            # Normalized central moments
            mu20 = moments['mu20'] / (moments['m00'] ** 2)
            mu02 = moments['mu02'] / (moments['m00'] ** 2)
            mu11 = moments['mu11'] / (moments['m00'] ** 2)

            # Eccentricity and orientation-independent features
            trace = mu20 + mu02
            det = mu20 * mu02 - mu11 ** 2

            # Eigenvalues of the covariance matrix (rotation invariant)
            lambda1 = 0.5 * (trace + np.sqrt(trace**2 - 4*det)) if trace**2 >= 4*det else trace/2
            lambda2 = 0.5 * (trace - np.sqrt(trace**2 - 4*det)) if trace**2 >= 4*det else trace/2

            eccentricity = np.sqrt(1 - lambda2/lambda1) if lambda1 > 0 else 0

            # Fourier descriptors for shape (rotation invariant)
            fourier_features = self._calculate_fourier_descriptors(pts)

            self.rotation_invariant_features = np.array([
                eccentricity,
                lambda1,
                lambda2,
                det,
                *fourier_features
            ])
        else:
            self.rotation_invariant_features = np.zeros(8)

    def _calculate_fourier_descriptors(self, pts, n_descriptors=4):
        """Calculate rotation-invariant Fourier descriptors"""
        if len(pts) < 3:
            return np.zeros(n_descriptors)

        # Convert to complex representation
        complex_pts = pts[:, 0] + 1j * pts[:, 1]

        # Center the shape
        centroid = np.mean(complex_pts)
        complex_pts = complex_pts - centroid

        # Calculate Fourier transform
        fft_pts = np.fft.fft(complex_pts)

        # Take magnitude (rotation invariant) and normalize
        magnitudes = np.abs(fft_pts)
        if len(magnitudes) > 1 and magnitudes[1] > 0:
            # Normalize by the first harmonic to make scale invariant
            magnitudes = magnitudes / magnitudes[1]

        # Return first few descriptors (excluding DC component)
        descriptors = magnitudes[1:min(len(magnitudes), n_descriptors+1)]

        # Pad with zeros if needed
        if len(descriptors) < n_descriptors:
            descriptors = np.pad(descriptors, (0, n_descriptors - len(descriptors)))

        return descriptors[:n_descriptors]


class ShapeMatcher:
    """
    Enhanced shape matcher with rotation robustness for flake matching.
    Uses multiple feature types for robust matching under rotation.
    """

    def __init__(self, hu_weight=None, descriptor_weight=None, class_weight=None,
                 rotation_invariant_weight=0.3):
        weights = SHAPE_MATCHING_WEIGHTS
        self.hu_weight = hu_weight or weights['hu_weight']
        self.descriptor_weight = descriptor_weight or weights['descriptor_weight']
        self.class_weight = class_weight or weights['class_weight']
        self.rotation_invariant_weight = rotation_invariant_weight

        # Normalize weights
        total_weight = (self.hu_weight + self.descriptor_weight +
                       self.class_weight + self.rotation_invariant_weight)
        self.hu_weight /= total_weight
        self.descriptor_weight /= total_weight
        self.class_weight /= total_weight
        self.rotation_invariant_weight /= total_weight

    def calculate_similarity(self, flake1: Flake, flake2: Flake) -> float:
        """
        Calculate enhanced similarity score between two flakes (0-1, higher is more similar).
        Uses multiple rotation-invariant features for robustness.
        """
        # Class matching score
        class_score = 1.0 if flake1.class_name == flake2.class_name else 0.0

        # Enhanced Hu moments comparison
        hu_score = self._calculate_hu_similarity(flake1.hu_moments, flake2.hu_moments)

        # Basic descriptor comparison
        desc_score = self._calculate_descriptor_similarity(
            flake1.shape_descriptor, flake2.shape_descriptor)

        # Rotation-invariant features comparison
        rotation_score = self._calculate_rotation_invariant_similarity(
            flake1.rotation_invariant_features, flake2.rotation_invariant_features)

        # Weighted combination
        total_score = (self.hu_weight * hu_score +
                      self.descriptor_weight * desc_score +
                      self.class_weight * class_score +
                      self.rotation_invariant_weight * rotation_score)

        return total_score

    def _calculate_hu_similarity(self, hu1, hu2):
        """Enhanced Hu moments similarity calculation"""
        if hu1 is None or hu2 is None:
            return 0.0

        # Use log transformation for better numerical stability
        hu1_log = -np.sign(hu1) * np.log10(np.abs(hu1) + 1e-10)
        hu2_log = -np.sign(hu2) * np.log10(np.abs(hu2) + 1e-10)

        # Calculate distance with adaptive scaling
        hu_distance = np.linalg.norm(hu1_log - hu2_log)

        # Use adaptive threshold based on the magnitude of features
        adaptive_threshold = 5.0 + 0.1 * (np.linalg.norm(hu1_log) + np.linalg.norm(hu2_log))
        hu_score = np.exp(-hu_distance / adaptive_threshold)

        return hu_score

    def _calculate_descriptor_similarity(self, desc1, desc2):
        """Basic shape descriptor similarity"""
        if desc1 is None or desc2 is None:
            return 0.0

        desc_distance = np.linalg.norm(desc1 - desc2)
        desc_score = np.exp(-desc_distance / 3.0)

        return desc_score

    def _calculate_rotation_invariant_similarity(self, feat1, feat2):
        """Rotation-invariant features similarity"""
        if feat1 is None or feat2 is None:
            return 0.0

        # Normalize features to handle scale differences
        feat1_norm = feat1 / (np.linalg.norm(feat1) + 1e-10)
        feat2_norm = feat2 / (np.linalg.norm(feat2) + 1e-10)

        # Calculate similarity using cosine similarity (rotation invariant)
        cosine_sim = np.dot(feat1_norm, feat2_norm)

        # Convert to 0-1 range
        rotation_score = (cosine_sim + 1) / 2

        return rotation_score

    def find_matches(self, reference_flakes: List[Flake],
                    current_flakes: List[Flake],
                    threshold: float = 0.6) -> List[Tuple[Flake, Flake, float]]:
        """
        Find matching flakes between reference and current sets.
        Lower threshold for better rotation robustness.
        """
        matches = []
        used_current_flakes = set()

        # Sort reference flakes by area (larger flakes first for better matching)
        sorted_ref_flakes = sorted(reference_flakes, key=lambda f: f.area, reverse=True)

        for ref_flake in sorted_ref_flakes:
            best_match = None
            best_score = 0

            for curr_flake in current_flakes:
                if curr_flake.id in used_current_flakes:
                    continue

                score = self.calculate_similarity(ref_flake, curr_flake)
                if score > best_score and score > threshold:
                    best_score = score
                    best_match = curr_flake

            if best_match:
                matches.append((ref_flake, best_match, best_score))
                used_current_flakes.add(best_match.id)

        # Sort matches by score (best matches first)
        matches.sort(key=lambda x: x[2], reverse=True)
        return matches


class CoordinateTransformer:
    """
    Enhanced coordinate transformation with improved rotation handling.
    Handles coordinate transformation between reference and current positions.
    """

    def __init__(self):
        self.translation = np.array([0.0, 0.0])
        self.rotation = 0.0
        self.scale = 1.0
        self.transform_matrix = None
        self.confidence = 0.0

    def calculate_transform_from_pairs(self, ref_points: np.ndarray,
                                     curr_points: np.ndarray,
                                     allow_scale: bool = False,
                                     robust: bool = True) -> dict:
        """
        Calculate transformation from matched point pairs with enhanced robustness.
        """
        if len(ref_points) < 2:
            raise ValueError("Need at least 2 point pairs for transformation")

        ref_center = np.mean(ref_points, axis=0)
        curr_center = np.mean(curr_points, axis=0)

        ref_centered = ref_points - ref_center
        curr_centered = curr_points - curr_center

        if len(ref_points) == 2:
            # Two-point transformation
            self._calculate_two_point_transform(ref_centered, curr_centered, allow_scale)
        else:
            # Multi-point transformation with enhanced robustness
            if robust:
                self._calculate_robust_transform(ref_centered, curr_centered, allow_scale)
            else:
                self._calculate_svd_transform(ref_centered, curr_centered, allow_scale)

        # Build transformation matrix
        R_matrix = np.array([[np.cos(self.rotation), -np.sin(self.rotation)],
                           [np.sin(self.rotation), np.cos(self.rotation)]])
        self.translation = curr_center - self.scale * R_matrix @ ref_center

        self.transform_matrix = np.eye(3)
        self.transform_matrix[:2, :2] = self.scale * R_matrix
        self.transform_matrix[:2, 2] = self.translation

        # Calculate transformation confidence
        self._calculate_confidence(ref_points, curr_points)

        return {
            'translation': self.translation,
            'rotation': self.rotation,
            'rotation_degrees': np.degrees(self.rotation),
            'scale': self.scale,
            'ref_center': ref_center,
            'curr_center': curr_center,
            'confidence': self.confidence
        }

    def _calculate_two_point_transform(self, ref_centered, curr_centered, allow_scale):
        """Calculate transformation from two points"""
        ref_vec = ref_centered[1] - ref_centered[0]
        curr_vec = curr_centered[1] - curr_centered[0]

        ref_angle = np.arctan2(ref_vec[1], ref_vec[0])
        curr_angle = np.arctan2(curr_vec[1], curr_vec[0])
        self.rotation = curr_angle - ref_angle

        # Normalize rotation to [-pi, pi]
        self.rotation = np.arctan2(np.sin(self.rotation), np.cos(self.rotation))

        if allow_scale:
            ref_dist = np.linalg.norm(ref_vec)
            curr_dist = np.linalg.norm(curr_vec)
            self.scale = curr_dist / ref_dist if ref_dist > 0 else 1.0
        else:
            self.scale = 1.0

    def _calculate_svd_transform(self, ref_centered, curr_centered, allow_scale):
        """Calculate transformation using SVD (Kabsch algorithm)"""
        H = ref_centered.T @ curr_centered
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T

        # Ensure proper rotation (det(R) = 1)
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T

        self.rotation = np.arctan2(R[1, 0], R[0, 0])

        if allow_scale:
            # Calculate scale as ratio of RMS distances
            ref_rms = np.sqrt(np.mean(np.sum(ref_centered**2, axis=1)))
            curr_rms = np.sqrt(np.mean(np.sum(curr_centered**2, axis=1)))
            self.scale = curr_rms / ref_rms if ref_rms > 0 else 1.0
        else:
            self.scale = 1.0

    def _calculate_robust_transform(self, ref_centered, curr_centered, allow_scale):
        """Calculate robust transformation using iterative refinement"""
        # Start with SVD solution
        self._calculate_svd_transform(ref_centered, curr_centered, allow_scale)

        # Iterative refinement for robustness
        for iteration in range(3):
            # Apply current transformation
            R_matrix = np.array([[np.cos(self.rotation), -np.sin(self.rotation)],
                               [np.sin(self.rotation), np.cos(self.rotation)]])
            transformed_ref = self.scale * (R_matrix @ ref_centered.T).T

            # Calculate residuals
            residuals = np.linalg.norm(transformed_ref - curr_centered, axis=1)

            # Weight points by inverse residuals (robust estimation)
            weights = 1.0 / (residuals + np.median(residuals) * 0.1)
            weights /= np.sum(weights)

            # Weighted centroid adjustment
            weighted_ref = ref_centered * weights[:, np.newaxis]
            weighted_curr = curr_centered * weights[:, np.newaxis]

            # Recalculate transformation with weights
            H_weighted = weighted_ref.T @ weighted_curr
            U, S, Vt = np.linalg.svd(H_weighted)
            R = Vt.T @ U.T

            if np.linalg.det(R) < 0:
                Vt[-1, :] *= -1
                R = Vt.T @ U.T

            self.rotation = np.arctan2(R[1, 0], R[0, 0])

    def _calculate_confidence(self, ref_points, curr_points):
        """Calculate confidence in the transformation"""
        if self.transform_matrix is None:
            self.confidence = 0.0
            return

        # Transform reference points and calculate errors
        errors = []
        for ref_pt, curr_pt in zip(ref_points, curr_points):
            transformed_pt = self.transform_point(ref_pt)
            error = np.linalg.norm(transformed_pt - curr_pt)
            errors.append(error)

        # Confidence based on mean error (lower error = higher confidence)
        mean_error = np.mean(errors)
        self.confidence = np.exp(-mean_error / 10.0)  # Exponential decay

    def transform_point(self, point: np.ndarray) -> np.ndarray:
        """Transform a point from reference to current coordinates"""
        if self.transform_matrix is None:
            raise ValueError("Transformation not calculated yet")

        point_h = np.append(point, 1)
        transformed_h = self.transform_matrix @ point_h
        return transformed_h[:2]

    def inverse_transform_point(self, point: np.ndarray) -> np.ndarray:
        """Transform a point from current to reference coordinates"""
        if self.transform_matrix is None:
            raise ValueError("Transformation not calculated yet")

        inv_matrix = np.linalg.inv(self.transform_matrix)
        point_h = np.append(point, 1)
        transformed_h = inv_matrix @ point_h
        return transformed_h[:2]


class ChipAligner:
    """
    Enhanced chip alignment with improved rotation robustness.
    Main class for chip alignment with rotation-robust algorithms.
    """

    def __init__(self, min_matches: int = None, ransac_threshold: float = None,
                 max_rotation_deg: float = 45.0):
        params = ALIGNMENT_PARAMS
        self.shape_matcher = ShapeMatcher()
        self.transformer = CoordinateTransformer()
        self.min_matches = min_matches or params['min_matches']
        self.ransac_threshold = ransac_threshold or params['ransac_threshold']
        self.max_rotation_deg = max_rotation_deg

    def select_locator_flakes(self, flakes: List[Flake],
                            n_locators: int = None) -> List[Flake]:
        """
        Select the best locator flakes with enhanced criteria for rotation robustness.
        """
        n_locators = n_locators or ALIGNMENT_PARAMS['default_n_locators']

        if len(flakes) <= n_locators:
            return flakes

        # Enhanced scoring for rotation robustness
        scores = []
        for i, flake in enumerate(flakes):
            # Size score (larger flakes are more stable)
            size_score = np.log(flake.area + 1) / 10.0

            # Uniqueness score (based on rotation-invariant features)
            uniqueness = 0
            for j, other in enumerate(flakes):
                if i != j:
                    similarity = self.shape_matcher.calculate_similarity(flake, other)
                    uniqueness += (1 - similarity)
            uniqueness /= (len(flakes) - 1)

            # Shape complexity score (more complex shapes are better locators)
            complexity_score = 0
            if flake.rotation_invariant_features is not None:
                complexity_score = np.std(flake.rotation_invariant_features)

            # Circularity penalty (circular shapes are less distinctive)
            circularity = flake.shape_descriptor[3] if len(flake.shape_descriptor) > 3 else 0
            circularity_penalty = 1.0 - circularity

            # Combined score
            score = (size_score * 0.3 + uniqueness * 0.4 +
                    complexity_score * 0.2 + circularity_penalty * 0.1)
            scores.append((score, flake))

        # Sort by score and select top candidates
        scores.sort(key=lambda x: x[0], reverse=True)

        # Ensure spatial distribution
        selected = [scores[0][1]]
        min_distance = ALIGNMENT_PARAMS['min_locator_distance_um']

        for score, flake in scores[1:]:
            # Check distance to already selected flakes
            min_dist = float('inf')
            for sel_flake in selected:
                dist = np.sqrt((flake.real_x_um - sel_flake.real_x_um)**2 +
                             (flake.real_y_um - sel_flake.real_y_um)**2)
                min_dist = min(min_dist, dist)

            if min_dist > min_distance:
                selected.append(flake)
                if len(selected) >= n_locators:
                    break

        return selected

    def align_chips(self, reference_flakes: List[Flake],
                   current_flakes: List[Flake],
                   locator_flakes: Optional[List[Flake]] = None) -> dict:
        """
        Enhanced chip alignment with rotation robustness.
        Align current chip to reference position with improved algorithms.
        """
        ref_set = locator_flakes if locator_flakes else reference_flakes

        # Enhanced matching with lower threshold for rotation robustness
        matches = self.shape_matcher.find_matches(ref_set, current_flakes, threshold=0.5)

        if len(matches) < self.min_matches:
            return {
                'success': False,
                'error': f'Insufficient matches: {len(matches)} < {self.min_matches}',
                'matches': matches
            }

        ref_points = np.array([[m[0].real_x_um, m[0].real_y_um] for m in matches])
        curr_points = np.array([[m[1].real_x_um, m[1].real_y_um] for m in matches])

        # Enhanced RANSAC for robust transformation estimation
        if len(matches) > 3:
            best_inliers, best_transform = self._robust_ransac_estimation(
                ref_points, curr_points, matches)
        else:
            # Direct estimation for small number of matches
            transform = self.transformer.calculate_transform_from_pairs(
                ref_points, curr_points, allow_scale=False, robust=True)
            best_inliers = list(range(len(matches)))
            best_transform = transform

        # Calculate final transformation with all inliers
        if best_inliers:
            inlier_ref = ref_points[best_inliers]
            inlier_curr = curr_points[best_inliers]
            transform = self.transformer.calculate_transform_from_pairs(
                inlier_ref, inlier_curr, allow_scale=False, robust=True)
        else:
            transform = best_transform

        # Validate rotation is within acceptable range
        rotation_deg = abs(np.degrees(transform['rotation']))
        if rotation_deg > self.max_rotation_deg:
            return {
                'success': False,
                'error': f'Rotation too large: {rotation_deg:.1f}° > {self.max_rotation_deg}°',
                'matches': matches
            }

        # Calculate alignment statistics
        errors, aligned_matches = self._calculate_alignment_statistics(
            matches, best_inliers, transform)

        return {
            'success': True,
            'transform': transform,
            'matches': aligned_matches,
            'num_inliers': len(best_inliers),
            'mean_error': np.mean([e for i, e in enumerate(errors) if i in best_inliers]),
            'max_error': np.max([e for i, e in enumerate(errors) if i in best_inliers]),
            'transformer': self.transformer,
            'confidence': transform.get('confidence', 0.0)
        }

    def _robust_ransac_estimation(self, ref_points, curr_points, matches):
        """Enhanced RANSAC with adaptive parameters"""
        best_inliers = []
        best_transform = None
        n_iterations = min(200, len(matches) * 20)  # Adaptive iterations

        for iteration in range(n_iterations):
            # Sample points (prefer higher-scoring matches)
            match_scores = [m[2] for m in matches]
            probabilities = np.array(match_scores) / np.sum(match_scores)

            try:
                indices = np.random.choice(len(matches), 3, replace=False, p=probabilities)
            except:
                indices = np.random.choice(len(matches), 3, replace=False)

            sample_ref = ref_points[indices]
            sample_curr = curr_points[indices]

            transformer = CoordinateTransformer()
            try:
                transform = transformer.calculate_transform_from_pairs(
                    sample_ref, sample_curr, allow_scale=False, robust=False)
            except:
                continue

            # Check if rotation is reasonable
            if abs(np.degrees(transform['rotation'])) > self.max_rotation_deg:
                continue

            # Count inliers with adaptive threshold
            inliers = []
            adaptive_threshold = self.ransac_threshold * (1 + transform.get('confidence', 0))

            for i, (ref_pt, curr_pt) in enumerate(zip(ref_points, curr_points)):
                pred_pt = transformer.transform_point(ref_pt)
                error = np.linalg.norm(pred_pt - curr_pt)
                if error < adaptive_threshold:
                    inliers.append(i)

            # Update best solution
            if len(inliers) > len(best_inliers):
                best_inliers = inliers
                best_transform = transform
                self.transformer = transformer

        return best_inliers, best_transform

    def _calculate_alignment_statistics(self, matches, best_inliers, transform):
        """Calculate detailed alignment statistics"""
        errors = []
        aligned_matches = []

        for i, (ref_flake, curr_flake, score) in enumerate(matches):
            ref_pt = np.array([ref_flake.real_x_um, ref_flake.real_y_um])
            curr_pt = np.array([curr_flake.real_x_um, curr_flake.real_y_um])
            pred_pt = self.transformer.transform_point(ref_pt)
            error = np.linalg.norm(pred_pt - curr_pt)
            errors.append(error)

            aligned_matches.append({
                'reference_id': ref_flake.id,
                'current_id': curr_flake.id,
                'similarity_score': score,
                'alignment_error': error,
                'is_inlier': i in best_inliers
            })

        return errors, aligned_matches


def save_chip_reference(chip_id: str, flakes: List[Flake],
                       locator_flakes: List[Flake],
                       filepath: str):
    """Save chip reference data including locator flakes with enhanced features"""
    data = {
        'chip_id': chip_id,
        'timestamp': time.time(),
        'all_flakes': [
            {
                'id': f.id,
                'center_x': f.center_x,
                'center_y': f.center_y,
                'real_x_um': f.real_x_um,
                'real_y_um': f.real_y_um,
                'shape': f.shape,
                'class': f.class_name,
                'area': f.area,
                'perimeter': f.perimeter,
                'hu_moments': f.hu_moments.tolist() if f.hu_moments is not None else None,
                'rotation_invariant_features': (f.rotation_invariant_features.tolist()
                                              if f.rotation_invariant_features is not None else None)
            }
            for f in flakes
        ],
        'locator_flakes': [f.id for f in locator_flakes]
    }

    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)


def load_chip_reference(filepath: str) -> Tuple[List[Flake], List[str]]:
    """Load chip reference data with enhanced features"""
    with open(filepath, 'r') as f:
        data = json.load(f)

    flakes = []
    for f_data in data['all_flakes']:
        flake = Flake(
            id=f_data['id'],
            center_x=f_data['center_x'],
            center_y=f_data['center_y'],
            real_x_um=f_data['real_x_um'],
            real_y_um=f_data['real_y_um'],
            shape=f_data['shape'],
            class_name=f_data['class']
        )

        # Restore enhanced features if available
        if f_data.get('hu_moments'):
            flake.hu_moments = np.array(f_data['hu_moments'])
        if f_data.get('rotation_invariant_features'):
            flake.rotation_invariant_features = np.array(f_data['rotation_invariant_features'])

        flakes.append(flake)

    return flakes, data['locator_flakes']