================================================================================
SCANNING OPERATION LOG - 2025-06-30 13:04:12
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751259850\scan_adaptive_1751259852.csv
Grid Steps: 0 x 0
Edge Method: general
Chip Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751259850
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751259850\debug_screenshots
================================================================================

[2025-06-30 13:04:12.088] [INFO] [SYSTEM] Logging system initialized successfully
[2025-06-30 13:04:12.843] [INFO] [SYSTEM] Using custom scan folder: scan_1751259850
[2025-06-30 13:04:12.858] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-06-30 13:04:12.955] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-06-30 13:04:12.965] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-06-30 13:04:13.081] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-06-30 13:04:13.474] [INFO] [POSITION] Position feedback: (17.35, 0.00) μm
[2025-06-30 13:04:14.204] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-06-30 13:04:14.384] [INFO] [POSITION] Position feedback: (345.37, 0.00) μm
[2025-06-30 13:04:14.596] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-06-30 13:04:14.827] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-06-30 13:04:14.994] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-06-30 13:04:15.506] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 345.6) μm
[2025-06-30 13:04:15.516] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-06-30 13:04:15.654] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-06-30 13:04:15.664] [INFO] [STATUS] Creating automatic chip boundary reference...
[2025-06-30 13:04:15.702] [INFO] [STATUS] Using EdgeDetector for precise edge detection...
[2025-06-30 13:04:15.712] [INFO] [STATUS] Using EdgeDetector with basic edge detection...
[2025-06-30 13:04:15.953] [INFO] [STATUS] ✗ Validation failed: Missing line orientations (H:True, V:False)
[2025-06-30 13:04:15.964] [INFO] [STATUS] ✗ CRITICAL: Edge detection validation failed
[2025-06-30 13:04:15.976] [INFO] [STATUS]   → Detected edges do not meet quality requirements
[2025-06-30 13:04:15.987] [INFO] [STATUS]   → Insufficient line fitting or poor edge quality
[2025-06-30 13:04:15.998] [ERROR] [ERROR] ✗ CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 13:04:16.007] [ERROR] [ERROR] ⚠ Warning: Failed to set zero reference: CRITICAL: Failed to create chip boundary reference. Scanning cannot proceed.
[2025-06-30 13:04:16.022] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-06-30 13:04:16.034] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-06-30 13:04:16.044] [INFO] [STATUS] Scanning row 0 rightward...
[2025-06-30 13:04:20.418] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-06-30 13:04:20.428] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-06-30 13:04:20.554] [INFO] [POSITION] Position feedback: (-4.23, 0.00) μm
